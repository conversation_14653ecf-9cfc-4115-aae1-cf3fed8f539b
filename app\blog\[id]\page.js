'use client'
import Layout from "@/components/layout/Layout"
import data from "@/util/blog.json"
import Link from 'next/link'
import { useParams } from "next/navigation"
import { useEffect, useState } from "react"

export default function BlogDetails() {
    let Router = useParams()
    const [blogPost, setBlogPost] = useState(null)
    const id = Router.id

    useEffect(() => {
        setBlogPost(data.find((data) => data.id == id))
    }, [id])

    return (
        <>
            {blogPost && (
                <Layout headerStyle={1} footerStyle={1} headerCls="header-style-2 header-style-4" >
                    <section className="section-box box-content-blog-2 box-content-blog-post">
                        <div className="container">
                            <div className="text-center blog-head">
                                <span className="btn btn-brand-4-sm">{blogPost.category}</span>
                                <h2 className="heading-2 mb-20 mt-15">{blogPost.title}</h2>
                                <p className="text-lg">{blogPost.excerpt || "Explore this comprehensive guide to understand the latest trends and insights in digital marketing."}</p>
                                <div className="blog-meta mt-20">
                                    <span className="author">By {blogPost.author}</span>
                                    <span className="date mx-3">•</span>
                                    <span className="date">{blogPost.date}</span>
                                </div>
                            </div>
                            <div className="row">
                                <div className="col-lg-1" />
                                <div className="col-lg-10">
                                    <div className="box-content-detail-blog">
                                        <div className="box-image-header">
                                            <img alt="OMX Digital Blog" src={`/assets/imgs/page/blog/${blogPost.img}`} />
                                        </div>
                                        <div className="box-detail-info">
                                            {/* Introduction */}
                                            {blogPost.content?.introduction && (
                                                <div className="blog-introduction mb-50">
                                                    <p className="text-lg color-grey-900 leading-relaxed">{blogPost.content.introduction}</p>
                                                </div>
                                            )}

                                            {/* Main Content Sections */}
                                            {blogPost.content?.sections?.map((section, sectionIndex) => (
                                                <div key={sectionIndex} className="blog-section mb-60">
                                                    <h3 className="heading-3 mb-30 color-brand-1">{section.heading}</h3>
                                                    
                                                    {/* Section content */}
                                                    {section.content && (
                                                        <p className="text-lg-regular color-grey-700 mb-25 leading-relaxed">{section.content}</p>
                                                    )}
                                                    
                                                    {/* Section bullet points */}
                                                    {section.bulletPoints && (
                                                        <ul className="list-check mb-30">
                                                            {section.bulletPoints.map((point, pointIndex) => (
                                                                <li key={pointIndex} className="text-base color-grey-700">
                                                                    {point}
                                                                </li>
                                                            ))}
                                                        </ul>
                                                    )}
                                                    
                                                    {/* Section conclusion */}
                                                    {section.conclusion && (
                                                        <p className="text-lg color-brand-2 font-medium mb-25">{section.conclusion}</p>
                                                    )}

                                                    {/* Sub-sections */}
                                                    {section.subSections?.map((subSection, subIndex) => (
                                                        <div key={subIndex} className="blog-subsection mb-40 pl-20 border-l-2 border-brand-2">
                                                            <h4 className="heading-4 mb-20 color-brand-2">{subSection.subHeading}</h4>
                                                            <p className="text-base color-grey-700 mb-20 leading-relaxed">{subSection.content}</p>
                                                            
                                                            {/* Sub-section bullet points */}
                                                            {subSection.bulletPoints && (
                                                                <ul className="list-check mb-20">
                                                                    {subSection.bulletPoints.map((point, pointIndex) => (
                                                                        <li key={pointIndex} className="text-base color-grey-600">
                                                                            {point}
                                                                        </li>
                                                                    ))}
                                                                </ul>
                                                            )}
                                                            
                                                            {/* Sub-section conclusion */}
                                                            {subSection.conclusion && (
                                                                <p className="text-base color-brand-1 font-medium italic">{subSection.conclusion}</p>
                                                            )}
                                                        </div>
                                                    ))}
                                                </div>
                                            ))}

                                            {/* Call to Action */}
                                            {blogPost.content?.callToAction && (
                                                <div className="blog-cta mt-60 p-40 bg-grey-100 rounded-3xl box-shadow-4 border-brand-2 border-1">
                                                    <h4 className="heading-4 mb-20 color-brand-1">{blogPost.content.callToAction.heading}</h4>
                                                    <p className="text-lg color-grey-700 mb-30 leading-relaxed">{blogPost.content.callToAction.content}</p>
                                                    <div className="d-flex justify-content-center">
                                                    <Link href="/form" className="btn btn-brand-4-medium hover-up hover-shadow-3 ">
                                                        Get Started Today
                                                        <svg width={22} height={22} viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M22 11.0003L18.4791 7.47949V10.3074H0V11.6933H18.4791V14.5213L22 11.0003Z" fill="true"></path>
                                                        </svg>
                                                    </Link>
                                                    </div>
                                                </div>
                                            )}

                                            {/* Fallback content for blogs without structured content */}
                                            {!blogPost.content && (
                                                <div className="fallback-content">
                                                    <p>Design comps, layouts, wireframes—will your clients accept that you go about things the facile way? Authorities in our business will tell in no uncertain terms that Lorem Ipsum is that huge, huge no no to forswear forever.</p>
                                                    <p>Not so fast, I'd say, there are some redeeming factors in favor of greeking text, as its use is merely the symptom of a worse problem to take into consideration.</p>
                                                    <p>The toppings you may chose for that TV dinner pizza slice when you forgot to shop for foods, the paint you may slap on your face to impress the new boss is your business. But what about your daily bread?</p>
                                                    <img src="/assets/imgs/page/blog/img-detail2.png" alt="Blog Detail" />
                                                    <p>Design comps, layouts, wireframes—will your clients accept that you go about things the facile way? Authorities in our business will tell in no uncertain terms that Lorem Ipsum is that huge, huge no no to forswear forever.</p>
                                                    <blockquote>Design comps, layouts, wireframes—we believe that clients will surely accept that you go about things the facile way. It's a matter of time.</blockquote>
                                                    <p>Not so fast, I'd say, there are some redeeming factors in favor of greeking text, as its use is merely the symptom of a worse problem to take into consideration.</p>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    <section className="section-box box-content-recommended">
                        <div className="container">
                            <div className="text-center">
                                <h2 className="mb-55">Recommended Articles</h2>
                            </div>
                            <div className="row">
                                <div className="col-lg-4">
                                    <div className="card-news-style-2">
                                        <div className="card-image">
                                            <Link href="/blog-post">
                                                <img src="/assets/imgs/page/blog/detail.png" alt="OMX Digital" />
                                            </Link>
                                        </div>
                                        <div className="card-info">
                                            <div className="card-meta">
                                                <Link className="btn btn-tag-sm" href="/blog-post">Technology</Link>
                                                <span className="date-post">16 October 2023</span>
                                            </div>
                                            <div className="card-title">
                                                <Link className="link-new" href="/blog-post">Savvy brand marketing: from branding basics to key strategies</Link>
                                            </div>
                                            <div className="card-more">
                                                <Link className="btn btn-learmore-2" href="/blog-post">Read More
                                                    <svg width={13} height={13} viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <g clipPath="url(#clip0_599_4830)">
                                                            <path d="M10.6537 3.8149L1.71801 12.7506L0.25 11.2826L9.18469 2.3469H1.31V0.270508H12.7301V11.6906H10.6537V3.8149Z" fill="true" />
                                                        </g>
                                                        <defs>
                                                            <clipPath id="clip0_599_4830">
                                                                <rect width={13} height={13} fill="white" />
                                                            </clipPath>
                                                        </defs>
                                                    </svg>
                                                </Link>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="col-lg-4">
                                    <div className="card-news-style-2">
                                        <div className="card-image">
                                            <Link href="/blog-post">
                                                <img src="/assets/imgs/page/blog/detail2.png" alt="OMX Digital" />
                                            </Link>
                                        </div>
                                        <div className="card-info">
                                            <div className="card-meta">
                                                <Link className="btn btn-tag-sm" href="/blog-post">Technology</Link>
                                                <span className="date-post">16 October 2023</span>
                                            </div>
                                            <div className="card-title">
                                                <Link className="link-new" href="/blog-post">110 drawing ideas to improve your skills you must know in this year</Link>
                                            </div>
                                            <div className="card-more">
                                                <Link className="btn btn-learmore-2" href="/blog-post">Read More
                                                    <svg width={13} height={13} viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <g clipPath="url(#clip0_599_4830)">
                                                            <path d="M10.6537 3.8149L1.71801 12.7506L0.25 11.2826L9.18469 2.3469H1.31V0.270508H12.7301V11.6906H10.6537V3.8149Z" fill="true" />
                                                        </g>
                                                        <defs>
                                                            <clipPath id="clip0_599_4830">
                                                                <rect width={13} height={13} fill="white" />
                                                            </clipPath>
                                                        </defs>
                                                    </svg>
                                                </Link>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="col-lg-4">
                                    <div className="card-news-style-2">
                                        <div className="card-image">
                                            <Link href="/blog-post">
                                                <img src="/assets/imgs/page/blog/detail3.png" alt="OMX Digital" />
                                            </Link>
                                        </div>
                                        <div className="card-info">
                                            <div className="card-meta">
                                                <Link className="btn btn-tag-sm" href="/blog-post">Technology</Link>
                                                <span className="date-post">16 October 2023</span>
                                            </div>
                                            <div className="card-title">
                                                <Link className="link-new" href="/blog-post">Perfect product images with Generative AI in OMX Digital platform</Link>
                                            </div>
                                            <div className="card-more">
                                                <Link className="btn btn-learmore-2" href="/blog-post">Read More
                                                    <svg width={13} height={13} viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <g clipPath="url(#clip0_599_4830)">
                                                            <path d="M10.6537 3.8149L1.71801 12.7506L0.25 11.2826L9.18469 2.3469H1.31V0.270508H12.7301V11.6906H10.6537V3.8149Z" fill="true" />
                                                        </g>
                                                        <defs>
                                                            <clipPath id="clip0_599_4830">
                                                                <rect width={13} height={13} fill="white" />
                                                            </clipPath>
                                                        </defs>
                                                    </svg>
                                                </Link>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </Layout>
            )}
        </>
    )
}
