"use client";
import { motion } from "framer-motion";
import Layout from "@/components/layout/Layout";
import Reach from "@/components/sections/home1/Reach";
import LogoTicker from "@/components/elements/LogoTicker";
import Link from "next/link";

export default function LeadForm() {

  return (
    <>
      <Layout
        headerStyle={1}
        footerStyle={1}
        headerCls="header-style-2 header-style-4"
      >
        <div>
          {/* Hero Section */}
          <section className="section-box box-content-contact">
            <div className="container">
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
              >
                <div className="text-center contact-head">
                  <span className="icon-1 shape-1" />
                  <span className="icon-2 shape-2" />
                  <span className="btn btn-brand-4-sm">Get Started</span>
                  <h1 className="heading-2 mb-20 mt-15">
                    Unlock Smart Automation & AI for Your Business
                  </h1>
                  <p className="text-lg mb-30">
                    Transform operations, sales, and customer communications with <br/>cutting-edge AI-powered SaaS and marketing automation.
                  </p>
                  <div className="text-center">
                    <nav className="container-breadcrumb">
                      <ul className="breadcrumb">
                        <li className="breadcrumb-item">
                          <Link href="/">Home</Link>
                        </li>
                        <li
                          className="breadcrumb-item active"
                          aria-current="page"
                        >
                          Get Started
                        </li>
                      </ul>
                    </nav>
                  </div>
                </div>
              </motion.div>
            </div>
          </section>

          {/* Main Form Section */}
          <section className=" ">
            <div className="container">
              <div className="row">
                {/* Left Column - Benefits */}
                <div className="col-lg-6 mb-30">
                  <div className="row mt-50">
                    <div className="col-12">
                      <h2 className="mb-20 mt-20">Get Started with Customized Automation</h2>
                      <p className="text-md neutral-700 mb-30">
                        Tell us about your business needs — our experts will tailor a solution you'll love!
                      </p>
                    </div>

                    {/* Highlights */}
                    <div className="col-lg-12">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-flow/aibot.gif" alt="AI Automation" />
                        </div>
                        <div className="card-info">
                          <h3 className="text-22-bold">AI-Powered Business Automation</h3>
                          <p className="text-md neutral-700">
                            Advanced marketing tools and business automation solutions powered by cutting-edge AI technology.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="col-lg-12">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sales/whats.gif" alt="WhatsApp Integration" />
                        </div>
                        <div className="card-info">
                          <h3 className="text-22-bold">Seamless WhatsApp Integration</h3>
                          <p className="text-md neutral-700">
                            Complete WhatsApp integration for support, sales, and lead nurturing with automated workflows.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="col-lg-12">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-flow/assign.gif" alt="Custom Solutions" />
                        </div>
                        <div className="card-info">
                          <h3 className="text-22-bold">Custom Solutions</h3>
                          <p className="text-md neutral-700">
                            Tailored automation solutions designed for both startups and enterprises with scalable architecture.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="col-lg-12">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/contact/call .gif" alt="Fast Onboarding" />
                        </div>
                        <div className="card-info">
                          <h3 className="text-22-bold">Fast Onboarding & Support</h3>
                          <p className="text-md neutral-700">
                            Quick implementation with comprehensive onboarding and ongoing expert support for your success.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Right Column - Form */}
                <div className="col-lg-6 mb-30">
                  <div className="block-form-contact mt-45">
                    

                    {/* Form iframe */}
                    <iframe
                      src="https://login.omxsales.com/widget/form/680cd425973e8"
                      style={{ 
                        width: "100%", 
                        height: "800px", 
                        border: "none", 
                        borderRadius: "8px",
                        boxShadow: "0 4px 20px rgba(0,0,0,0.1)"
                      }}
                      title="OMX Digital Lead Form"
                    />

                    {/* Privacy Notice */}
                    <div className="text-center mt-20">
                      <p className="text-sm neutral-500" style={{ fontSize: "12px", fontStyle: "italic" }}>
                         Your information is confidential and used solely to connect you with our solution experts.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
           {/* Statistics Section */}
          <Reach />
          {/* Trusted Partners Section */}
                    <section className="section-box wow animate__animated animate__fadeIn " style={{ paddingTop: "20px", paddingBottom: "40px" }}>
                      <div className="container">
                        <div className="text-center mb-30">
                          <span className="btn btn-brand-4-sm mb-15">Trusted Partners</span>
                          <h2 className="heading-2 mb-15">
                            Join Thousands of Businesses Already Automating Success
                          </h2>
                          <p className="text-md neutral-700">
                            Leading companies trust OMX Digital to power their automation and drive growth
                          </p>
                        </div>
                        <div className="carouselTickerLogos2 carouselTicker_vertical" id="slide-logos">
                          <LogoTicker />
                        </div>
                      </div>
                    </section>
        </div>
      </Layout>
    </>
  );
}
