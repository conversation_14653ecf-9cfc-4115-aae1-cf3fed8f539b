import Layout from "@/components/layout/Layout";
import Faq from "@/components/sections/omx-flow/Faq";
import Section1 from "@/components/sections/omx-flow/Section1";
import Section2 from "@/components/sections/omx-flow/Section2";
import Section3 from "@/components/sections/omx-flow/Section3";
import FeaturesSection from "@/components/sections/omx-flow/FeaturesSection";
import Who from "@/components/sections/omx-flow/Who";
import Working from "@/components/sections/omx-flow/Working";
import CaseStudies from "@/components/sections/omx-flow/CaseStudies";
import Link from "next/link";

export default function omxflow() {
  return (
    <>
      <Layout headerStyle={4} footerStyle={1} chatbotType={3} logoWhite>
        <Section1 />
        <Section2 />
        <Section3 />
        <FeaturesSection />
        <Who />
        <Working />
        <Faq />
        <CaseStudies />
      </Layout>
    </>
  );
}
