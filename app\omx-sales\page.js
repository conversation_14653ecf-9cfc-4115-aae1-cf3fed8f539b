import Layout from "@/components/layout/Layout";
import Faq from "@/components/sections/omx-sales/Faq";
import Section1 from "@/components/sections/omx-sales/Section1";
import Section2 from "@/components/sections/omx-sales/Section2";
import Section3 from "@/components/sections/omx-sales/Section3";
import Pricing from "@/components/sections/omx-sales/Pricing"; // This will be your animated version
import FeaturesSection from "@/components/sections/omx-sales/FeaturesSection"; // New animated features section
import Who from "@/components/sections/omx-sales/Who";
import Working from "@/components/sections/omx-sales/Working";
import CaseStudies from "@/components/sections/omx-sales/CaseStudies";
import Link from "next/link";

export default function omxsales() {
  return (
    <>
      <Layout headerStyle={2} footerStyle={1} chatbotType={1} mobileMenuStyle={1} logoWhite>
        <Section1 />
        <Section2 />
        <Section3 /> {/* Your animated section from the previous code */}
        <FeaturesSection /> {/* New animated features section */}
        <Pricing/>
        <Who />
        <Working />
        <Faq />
        <CaseStudies />
      </Layout>
    </>
  );
}
