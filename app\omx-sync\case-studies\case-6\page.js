import Layout from "@/components/layout/Layout";
import Link from 'next/link';
import { caseStudiesData, challengesData, solutionsData, resultsData, reflectionData } from "@/data/omx-sync/caseStudiesData";

export default function CaseStudy1() {
  // Use first case study by default
  const id = 6;
  const caseStudy = caseStudiesData.find(study => study.id === id) || caseStudiesData[0];
  const challenges = challengesData.find(item => item.id === id) || challengesData[0];
  const solutions = solutionsData.find(item => item.id === id) || solutionsData[0];
  const results = resultsData.find(item => item.id === id) || resultsData[0];
  const reflection = reflectionData.find(item => item.id === id) || reflectionData[0];
  return (
    <>
      <Layout headerStyle={1} footerStyle={1} headerCls="header-style-2 header-style-4">
        <section className="section-box box-content-blog-2 box-content-blog-post">
          <div className="container">
            <div className="text-center blog-head">
              <span className="btn btn-brand-4-sm">OMX FLOW</span>
              <h2 className="heading-2 mb-20 mt-15">Case Study on</h2>
              <h2 className="heading-2 mb-20 mt-15">{caseStudy.heading}</h2>
              <p className="text-lg"><strong>Client:</strong> {caseStudy.client}</p>
            </div>
            
            <div className="row">
              <div className="col-lg-1" />
              <div className="col-lg-10">
                <div className="box-content-detail-blog">
                  <div className="box-image-header">
                    <img alt={caseStudy.heading} src={caseStudy.image} />
                  </div>
                  
                  <div className="box-detail-info mt-30">
                    <blockquote><h3>Challenges</h3></blockquote>
                    
                    <h6 className="challengehead">{challenges.subheading}</h6>
                    <div className="d-flex align-items-center mb-15 mt-20">
                      <p className="mb-0">{challenges.p1}</p>
                    </div>
                    <div className="d-flex align-items-center mb-15">
                      <p className="mb-0">{challenges.p2}</p>
                    </div>
                    <div className="d-flex align-items-center mb-15">
                      <p className="mb-0">{challenges.p3}</p>
                    </div>
                    
                    <blockquote><h3>The OMX FLOW Solution</h3></blockquote>
                    
                    <div className="mt-20 mb-30">
                      <div className="d-flex align-items-center mb-15">
                        <p className="mb-0"><strong>{solutions.strong1}</strong> {solutions.p1}</p>
                      </div>
                      <div className="d-flex align-items-center mb-15">
                        <p className="mb-0"><strong>{solutions.strong2}</strong> {solutions.p2}</p>
                      </div>
                      <div className="d-flex align-items-center mb-15">
                        <p className="mb-0"><strong>{solutions.strong3}</strong> {solutions.p3}</p>
                      </div>
                    </div>
                    
                    <blockquote><h3>Results & Impact</h3></blockquote>
                    
                    <div className="mt-20 mb-30">
                      <div className="d-flex align-items-center mb-15">
                        <p className="mb-0"><strong>{results.strong1}</strong> {results.p1}</p>
                      </div>
                      <div className="d-flex align-items-center mb-15">
                        <p className="mb-0"><strong>{results.strong2}</strong> {results.p2}</p>
                      </div>
                      <div className="d-flex align-items-center mb-15">
                        <p className="mb-0"><strong>{results.strong3}</strong> {results.p3}</p>
                      </div>
                    </div>

                    <blockquote><h3>Reflection: </h3></blockquote>

                    <div className="mt-20 mb-30">
                      <div className="d-flex align-items-center mb-15">
                        <p className="mb-0"><strong>{reflection.strong1}</strong> {reflection.p1}</p>
                      </div>
                    </div>

                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </Layout>
    </>
  );
}