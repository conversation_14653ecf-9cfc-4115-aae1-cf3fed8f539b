import Layout from "@/components/layout/Layout";
import Faq from "@/components/sections/omx-sync/Faq";
import Section1 from "@/components/sections/omx-sync/Section1";
import Section2 from "@/components/sections/omx-sync/Section2";
import Section3 from "@/components/sections/omx-sync/Section3";
import Section4 from "@/components/sections/omx-sync/Section4";
import Section5 from "@/components/sections/omx-sync/Section5";
import Section6 from "@/components/sections/omx-sync/Section6";
import Section7 from "@/components/sections/omx-sync/Section7";
import Section8 from "@/components/sections/omx-sync/Section8";
import Section9 from "@/components/sections/omx-sync/Section9";
import Who from "@/components/sections/omx-sync/Who";
import Working from "@/components/sections/omx-sync/Working";
import CaseStudies from "@/components/sections/omx-sync/CaseStudies";
import Link from "next/link";
export default function omxsync() {
  return (
    <>
      <Layout headerStyle={3} footerStyle={1} chatbotType={2} mobileMenuStyle={2} logoWhite>
        <Section1 />
        <Section2 />
        <Section3 />
        <section
          className="section-box box-benifit box-our-working"
          style={{
            background:
              "linear-gradient(to bottom,rgb(193, 243, 209), #ffffff)",
          }}
        >
          <div className="container" id="features">
            {/* feature-1 */}
            <div className="box-border-rounded">
              <div className="row align-items-center">
                <div className="col-lg-6 mb-40 text-center text-lg-start">
                  <div className="image-feature-2">
                    <img
                      className="rounded"
                      src="/assets/imgs/page/omx-sync/task-management.png"
                      alt="OMX Digital"
                    />
                  </div>
                </div>
                <div className="col-lg-6 mb-40">
                  <h3 className="heading-3 mb-20">
                    Task Management & Automation
                  </h3>
                  <div className="row mt-50">
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/list.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Marketing Strategy</h3>
                          </Link> */}
                          <p className="text-md neutral-900">
                            Assign tasks via text or voice commands.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/hourglass_15579129.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Digital Services</h3>
                          </Link> */}
                          <p className="text-md neutral-700">
                            Set deadlines, priorities & recurring tasks with
                            ease.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/bell.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Product Selling</h3>
                          </Link> */}
                          <p className="text-md neutral-700">
                            Get real-time status updates & reminders.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* feature-2 */}
            <div className="box-border-rounded ">
              <div className="row align-items-center">
                <div className="col-lg-6 mb-40">
                  <h3 className="heading-3 mb-20">
                    Voice-Based Task Assignment
                  </h3>
                  <div className="row mt-50">
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/voice-assistant_18844008.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Marketing</h3>
                          </Link> */}
                          <p className="text-md neutral-700">
                            Assign & manage tasks using voice notes.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/process_15578431.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Strategy</h3>
                          </Link> */}
                          <p className="text-md neutral-700">
                            Automate workflows & speed up execution.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/chart_16045892.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Development</h3>
                          </Link> */}
                          <p className="text-md neutral-700">
                            Perfect for busy managers & remote teams.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="col-lg-6 mb-40 text-center text-lg-end">
                  <div className="image-feature-2">
                    <img
                      className="rounded"
                      src="/assets/imgs/page/omx-sync/voice-based.png"
                      alt="OMX Digital"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* feature-3 */}
          <div className="container">
            <div className="box-border-rounded">
              <div className="row align-items-center">
                <div className="col-lg-6 mb-40 text-center text-lg-start">
                  <div className="image-feature-2">
                    <img
                      className="rounded"
                      src="/assets/imgs/page/omx-sync/recuring.png"
                      alt="OMX Digital"
                    />
                  </div>
                </div>
                <div className="col-lg-6 mb-40">
                  <h3 className="heading-3 mb-20">
                    Recurring Tasks & Workflows
                  </h3>
                  <div className="row mt-50">
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img
                            className="rounded"
                            src="/assets/imgs/page/omx-sync/calendar_17110627.gif"
                          />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Marketing Strategy</h3>
                          </Link> */}
                          <p className="text-md neutral-900">
                            Automate repetitive daily, weekly, or monthly tasks.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/alarm_16046060.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Digital Services</h3>
                          </Link> */}
                          <p className="text-md neutral-700">
                            Set auto-reminders & alerts for upcoming deadlines.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/thinking_15579082.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Product Selling</h3>
                          </Link> */}
                          <p className="text-md neutral-700">
                            Reduce manual effort & improve productivity.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* feature-4 */}
            <div className="box-border-rounded">
              <div className="row align-items-center">
                <div className="col-lg-6 mb-40">
                  <h3 className="heading-3 mb-20">
                    Team Performance & Reporting
                  </h3>
                  <div className="row mt-50">
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/navigation_10606402.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Marketing</h3>
                          </Link> */}
                          <p className="text-md neutral-700">
                            Track productivity & efficiency across teams.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/idea_18830547.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Strategy</h3>
                          </Link> */}
                          <p className="text-md neutral-700">
                            Get AI-powered insights on task completion &
                            workload balance.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/search-engine_18559540.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Development</h3>
                          </Link> */}
                          <p className="text-md neutral-700">
                            Identify & reward top performers.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="col-lg-6 mb-40 text-center text-lg-end">
                  <div className="image-feature-2">
                    <img
                      className="rounded"
                      src="/assets/imgs/page/omx-sync/team.png"
                      alt="OMX Digital"
                    />
                  </div>
                </div>
              </div>
            </div>
            {/* feature-5 */}
            <div className="box-border-rounded">
              <div className="row align-items-center">
                <div className="col-lg-6 mb-40 text-center text-lg-start">
                  <div className="image-feature-2">
                    <img
                      className="rounded"
                      src="/assets/imgs/page/omx-sync/multi.png"
                      alt="OMX Digital"
                    />
                  </div>
                </div>
                <div className="col-lg-6 mb-40">
                  <h3 className="heading-3 mb-20">
                    Multi-Department Task Allocation
                  </h3>
                  <div className="row mt-50">
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/tasking_15578536.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Marketing Strategy</h3>
                          </Link> */}
                          <p className="text-md neutral-900">
                            Assign & track tasks across multiple teams &
                            departments.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/intimacy_19004010.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Digital Services</h3>
                          </Link> */}
                          <p className="text-md neutral-700">
                            Ensure smooth collaboration between sales,
                            marketing, HR, and operations.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/time-managament_17905442.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Product Selling</h3>
                          </Link> */}
                          <p className="text-md neutral-700">
                            Cross-functional task management for seamless
                            execution.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* feature-6 */}
            <div className="box-border-rounded">
              <div className="row align-items-center">
                <div className="col-lg-6 mb-40">
                  <h3 className="heading-3 mb-20">
                    Expense & Financial Tracking
                  </h3>
                  <div className="row mt-50">
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/analytics_19005382.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Marketing</h3>
                          </Link> */}
                          <p className="text-md neutral-700">
                            Monitor & manage business expenses in real-time.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/path_18992769.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Strategy</h3>
                          </Link> */}
                          <p className="text-md neutral-700">
                            Create approval workflows for better financial
                            control.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/corruption_12417287.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Development</h3>
                          </Link> */}
                          <p className="text-md neutral-700">
                            Reduce unnecessary costs & improve budgeting.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="col-lg-6 mb-40 text-center text-lg-end">
                  <div className="image-feature-2">
                    <img
                      className="rounded"
                      src="/assets/imgs/page/omx-sync/expenses.png"
                      alt="OMX Digital"
                    />
                  </div>
                </div>
              </div>
            </div>
            {/* feature-7 */}
            <div className="box-border-rounded">
              <div className="row align-items-center">
                <div className="col-lg-6 mb-40 text-center text-lg-start">
                  <div className="image-feature-2">
                    <img
                      className="rounded"
                      src="/assets/imgs/page/omx-sync/qr.png"
                      alt="OMX Digital"
                    />
                  </div>
                </div>
                <div className="col-lg-6 mb-40">
                  <h3 className="heading-3 mb-20">
                    QR-Based Attendance & Leave Management
                  </h3>
                  <div className="row mt-50">
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/qr-code_18873863.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Marketing Strategy</h3>
                          </Link> */}
                          <p className="text-md neutral-900">
                            Employees check-in via QR code-based tracking.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/questionnaire_12134209.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Digital Services</h3>
                          </Link> */}
                          <p className="text-md neutral-700">
                            Automate leave requests & approvals.
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-sync/route_10606384.gif" />
                        </div>
                        <div className="card-info">
                          {/* <Link href="#">
                            <h3 className="text-22-bold">Product Selling</h3>
                          </Link> */}
                          <p className="text-md neutral-700">
                            Location-based tracking for field & remote teams.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* <Section4 />
        <Section5 />
        <Section6 />
        <Section7 />
        <Section8 />
        <Section9 /> */}
        <Who />
        <Working />
        <Faq />
        <CaseStudies />
      </Layout>
    </>
  );
}
