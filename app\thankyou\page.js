"use client";
import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";
import { useRouter } from "next/navigation";
import Layout from "@/components/layout/Layout";
import LogoTicker from "@/components/elements/LogoTicker";
import Link from "next/link";

export default function ThankYou() {
  const router = useRouter();

  // Function to handle navigation to services section
  const handleViewSolutions = (e) => {
    e.preventDefault();

    // Check if we're already on the homepage
    if (window.location.pathname === '/') {
      const element = document.getElementById('services');
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    } else {
      // Navigate to homepage first, then scroll
      router.push('/');
      setTimeout(() => {
        const element = document.getElementById('services');
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }, 800); // Increased timeout for navigation
    }
  };

  // Animation variants
  const fadeInUp = {
    hidden: { opacity: 0, y: 60 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const cardVariant = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  // Refs for sections
  const heroRef = useRef(null);
  const mainRef = useRef(null);
  const cardsRef = useRef(null);
  const logoRef = useRef(null);

  // InView hooks
  const heroInView = useInView(heroRef, { once: true, threshold: 0.2 });
  const mainInView = useInView(mainRef, { once: true, threshold: 0.2 });
  const cardsInView = useInView(cardsRef, { once: true, threshold: 0.1 });
  const logoInView = useInView(logoRef, { once: true, threshold: 0.2 });

  return (
    <>
      <Layout
        headerStyle={1}
        footerStyle={1}
        headerCls="header-style-2 header-style-4"
      >
        <div>
          {/* Hero Section */}
          <motion.section
            className="section-box box-content-contact"
            ref={heroRef}
            initial="hidden"
            animate={heroInView ? "visible" : "hidden"}
            variants={fadeInUp}
          >
            <div className="container">
              <motion.div
                initial="hidden"
                animate={heroInView ? "visible" : "hidden"}
                variants={staggerContainer}
              >
                <div className="text-center contact-head">
                  <span className="icon-1 shape-1" />
                  <span className="icon-2 shape-2" />
                  <span className="btn btn-brand-4-sm">Thank You!</span>
                  <h1 className="heading-2 mb-20 mt-15">
                    Thank You for Your Interest in OMX Digital!
                  </h1>
                  <p className="text-lg mb-30">
                    We appreciate your inquiry — your details have been received.
                  </p>
                  <div className="text-center">
                    <nav className="container-breadcrumb">
                      <ul className="breadcrumb">
                        <li className="breadcrumb-item">
                          <Link href="/">Home</Link>
                        </li>
                        <li className="breadcrumb-item">
                          <Link href="/form">Get Started</Link>
                        </li>
                        <li
                          className="breadcrumb-item active"
                          aria-current="page"
                        >
                          Thank You
                        </li>
                      </ul>
                    </nav>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.section>

          {/* Main Content Section */}
          <motion.section
            className="section-box"
            style={{ paddingTop: "40px", paddingBottom: "20px" }}
            ref={mainRef}
            initial="hidden"
            animate={mainInView ? "visible" : "hidden"}
            variants={fadeInUp}
          >
            <div className="container">
              <div className="row justify-content-center">
                <div className="col-lg-10 col-xl-8">
                 

                  {/* What Happens Next Section */}
                  <div className="row">
                    <div className="col-lg-6 col-md-6 mb-30 mb-lg-40">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/contact/call .gif" alt="Expert Connection" />
                        </div>
                        <div className="card-info">
                          <h3 className="text-22-bold">What Happens Next?</h3>
                          <p className="text-md neutral-700">
                            Our experts will connect with you within 24 hours to discuss your goals and recommend the best solutions.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="col-lg-6 col-md-6 mb-30 mb-lg-40">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-flow/coaching.gif" alt="Free Consultation" />
                        </div>
                        <div className="card-info">
                          <h3 className="text-22-bold">Free Consultation</h3>
                          <p className="text-md neutral-700">
                            You'll receive a free consultation and walkthrough of our automation platform tailored to your needs.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="col-lg-6 col-md-6 mb-30 mb-lg-40">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-flow/settings.gif" alt="Custom Workflow" />
                        </div>
                        <div className="card-info">
                          <h3 className="text-22-bold">Custom Workflow Design</h3>
                          <p className="text-md neutral-700">
                            We'll answer your questions and help design the ideal workflow for your business requirements.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="col-lg-6 col-md-6 mb-30 mb-lg-40">
                      <div className="card-feature-2">
                        <div className="card-image">
                          <img src="/assets/imgs/page/omx-flow/speed.gif" alt="Implementation" />
                        </div>
                        <div className="card-info">
                          <h3 className="text-22-bold">Quick Implementation</h3>
                          <p className="text-md neutral-700">
                            Fast onboarding process with comprehensive training and ongoing support for your team.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  
                </div>
              </div>
            </div>
          </motion.section>

          <motion.section
            className="section-box"
            style={{ paddingTop: "20px", paddingBottom: "40px" }}
            ref={cardsRef}
            initial="hidden"
            animate={cardsInView ? "visible" : "hidden"}
            variants={fadeInUp}
          >
            <div className="container">
              {/* While You Wait Section */}
              <motion.div
                className="text-center mt-30 mb-30"
                variants={staggerContainer}
                initial="hidden"
                animate={cardsInView ? "visible" : "hidden"}
              >
                <motion.h2 className="heading-2 mb-40" variants={cardVariant}>
                  While You Wait…
                </motion.h2>
                <motion.div className="block-pricing" variants={cardVariant}>
                  <motion.div
                    className="row"
                    variants={staggerContainer}
                    initial="hidden"
                    animate={cardsInView ? "visible" : "hidden"}
                  >
                        <div className="col-lg-4 col-md-6 mb-30">
                          <div className="card-pricing card-pricing-style-2">
                            <div className="card-title">
                              <div style={{
                                width: "60px",
                                height: "60px",
                                backgroundColor: "#79c691",
                                borderRadius: "50%",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                margin: "0 auto 15px"
                              }}>
                                <svg width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="#191919" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  <path d="M2 17L12 22L22 17" stroke="#191919" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  <path d="M2 12L12 17L22 12" stroke="#191919" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                              </div>
                              <h6>Explore Our Solutions</h6>
                            </div>
                            <div className="card-lists">
                              <p className="text-md neutral-700 mb-25">
                                Learn more about our products and client success stories.
                              </p>
                            </div>
                            <div className="card-button">
                              <Link
                                className="btn btn-get-started"
                                href="/#services"
                                onClick={handleViewSolutions}
                              >
                                View Solutions
                                <svg width={23} height={8} viewBox="0 0 23 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M22.5 3.99934L18.9791 0.478516V3.30642H0.5V4.69236H18.9791V7.52031L22.5 3.99934Z" fill="true" />
                                </svg>
                              </Link>
                            </div>
                          </div>
                        </div>

                        <div className="col-lg-4 col-md-6 mb-30">
                          <div className="card-pricing card-pricing-style-2">
                            <div className="card-title">
                              <div style={{
                                width: "60px",
                                height: "60px",
                                backgroundColor: "#79c691",
                                borderRadius: "50%",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                margin: "0 auto 15px"
                              }}>
                                <svg width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="#191919" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  <path d="M14 2V8H20" stroke="#191919" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  <path d="M16 13H8" stroke="#191919" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  <path d="M16 17H8" stroke="#191919" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  <path d="M10 9H9H8" stroke="#191919" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                              </div>
                              <h6>Knowledge Base</h6>
                            </div>
                            <div className="card-lists">
                              <p className="text-md neutral-700 mb-25">
                                Get tips on automation, AI, and digital marketing.
                              </p>
                            </div>
                            <div className="card-button">
                              <Link className="btn btn-get-started" href="/omx-sales">
                                Read more
                                <svg width={23} height={8} viewBox="0 0 23 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M22.5 3.99934L18.9791 0.478516V3.30642H0.5V4.69236H18.9791V7.52031L22.5 3.99934Z" fill="true" />
                                </svg>
                              </Link>
                            </div>
                          </div>
                        </div>

                        <div className="col-lg-4 col-md-6 mb-30">
                          <div className="card-pricing card-pricing-style-2">
                            <div className="card-title">
                              <div style={{
                                width: "60px",
                                height: "60px",
                                backgroundColor: "#79c691",
                                borderRadius: "50%",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                margin: "0 auto 15px"
                              }}>
                                <svg width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="#191919" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                              </div>
                              <h6>Need Urgent Help?</h6>
                            </div>
                            <div className="card-lists">
                              <p className="text-md neutral-700 mb-20">
                                Contact us directly for immediate assistance mail us &nbsp; 
                              <Link
                                  href="mailto:<EMAIL>"
                                  className=""
                                >
                                  <EMAIL>
                                </Link></p>
                              
                            </div>
                            <div className="card-button">
                              <Link className="btn btn-get-started" href="https://wa.me/7063034038?text=support">
                                Live WhatsApp Chat
                                <svg width={23} height={8} viewBox="0 0 23 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M22.5 3.99934L18.9791 0.478516V3.30642H0.5V4.69236H18.9791V7.52031L22.5 3.99934Z" fill="true" />
                                </svg>
                              </Link>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    </motion.div>
                  </motion.div>
            </div>
          </motion.section>

         

          

          {/* Trusted Partners Section */}
          <motion.section
            className="section-box wow animate__animated animate__fadeIn box-logos-4"
            style={{ paddingTop: "20px", paddingBottom: "40px" }}
            ref={logoRef}
            initial="hidden"
            animate={logoInView ? "visible" : "hidden"}
            variants={fadeInUp}
          >
            <div className="container">
              <motion.div
                className="text-center mb-30"
                variants={staggerContainer}
                initial="hidden"
                animate={logoInView ? "visible" : "hidden"}
              >
                <motion.span className="btn btn-brand-4-sm mb-15" variants={cardVariant}>
                  Trusted Partners
                </motion.span>
                <motion.h2 className="heading-2 mb-15" variants={cardVariant}>
                  Join Thousands of Businesses Already Automating Success
                </motion.h2>
                <motion.p className="text-md neutral-700" variants={cardVariant}>
                  Leading companies trust OMX Digital to power their automation and drive growth
                </motion.p>
              </motion.div>
              <motion.div
                className="carouselTickerLogos2 carouselTicker_vertical"
                id="slide-logos"
                variants={cardVariant}
                initial="hidden"
                animate={logoInView ? "visible" : "hidden"}
              >
                <LogoTicker />
              </motion.div>
            </div>
          </motion.section>

          <motion.section
            className="section-box"
            style={{ paddingTop: "20px", paddingBottom: "40px" }}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, threshold: 0.2 }}
            variants={fadeInUp}
          >
            <div className="container">
              <div className="row justify-content-center">
                <div className="col-lg-6 col-md-8">
                  {/* Back to Home */}
                  <motion.div
                    className="text-center"
                    variants={cardVariant}
                  >
                    <Link href="/" className="btn btn-black rounded-3">
                      Back to Home
                      <svg
                        width={22}
                        height={8}
                        viewBox="0 0 22 8"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        style={{ marginLeft: "8px" }}
                      >
                        <path
                          d="M22 3.99934L18.4791 0.478516V3.30642H0V4.69236H18.4791V7.52031L22 3.99934Z"
                          fill="currentColor"
                        />
                      </svg>
                    </Link>
                  </motion.div>
                </div>
              </div>
            </div>
          </motion.section>
        </div>
      </Layout>
    </>
  );
}
