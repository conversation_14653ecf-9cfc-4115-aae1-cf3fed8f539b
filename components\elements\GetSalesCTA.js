"use client";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { X } from "lucide-react";

const buttonHover = {
  hover: {
    scale: 1.05,
    transition: { duration: 0.2 },
  },
};

export default function SalesCTA() {
  const [showDemoForm, setShowDemoForm] = useState(false);

  const toggleDemoForm = (e) => {
    e?.preventDefault?.();
    setShowDemoForm((prev) => !prev);
  };

  useEffect(() => {
    const handleEscKey = (e) => {
      if (e.key === "Escape") setShowDemoForm(false);
    };

    const handleClickOutside = (e) => {
      if (showDemoForm && e.target.className === "form-modal-overlay") {
        setShowDemoForm(false);
      }
    };

    document.addEventListener("keydown", handleEscKey);
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("keydown", handleEscKey);
      document.removeEventListener("click", handleClickOutside);
    };
  }, [showDemoForm]);

  return (
    <>
      <motion.div whileHover="hover" variants={buttonHover}>
        <a
          href="#"
          className="btn btn-get-started"
          onClick={toggleDemoForm}
        >
          Get Started
          <motion.svg
            width={22}
            height={22}
            viewBox="0 0 22 22"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            animate={{ x: [0, 5, 0] }}
            transition={{
              repeat: Infinity,
              repeatType: "reverse",
              duration: 1.5,
            }}
            style={{ marginLeft: "8px" }}
          >
            <path
              d="M22 11.0003L18.4791 7.47949V10.3074H0V11.6933H18.4791V14.5213L22 11.0003Z"
              fill="currentColor"
            />
          </motion.svg>
        </a>
      </motion.div>

      {showDemoForm && (
        <motion.div
          className="form-modal-overlay"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.7)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 9999,
          }}
        >
          <div
            className="form-modal-content"
            style={{
              backgroundColor: "#f8fff8",
              borderRadius: "8px",
              width: "90%",
              maxWidth: "600px",
              maxHeight: "90vh",
              overflow: "auto",
              position: "relative",
              padding: "20px",
              boxShadow: "0 4px 20px rgba(0,0,0,0.2)",
            }}
          >
            <button
              onClick={toggleDemoForm}
              style={{
                position: "absolute",
                top: "10px",
                right: "10px",
                background: "none",
                border: "none",
                cursor: "pointer",
                padding: "5px",
                borderRadius: "50%",
                backgroundColor: "#f0f0f0",
              }}
            >
              <X size={24} />
            </button>

            <div
              className="form-header"
              style={{ textAlign: "center", marginBottom: "25px" }}
            >
              <div
                className="logo-container"
                style={{
                  margin: "0 auto",
                  width: "120px",
                  height: "120px",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <img
                  src="/assets/imgs/page/omx-sales/omxiconn.png"
                  alt="OMX Sales Logo"
                  style={{ maxWidth: "100%", height: "100%" }}
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src =
                      'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="120" height="120" viewBox="0 0 120 120"><polygon fill="%2340C18E" points="60,20 90,40 90,80 60,100 30,80 30,40" /><circle fill="%23FFFFFF" cx="60" cy="60" r="15" /></svg>';
                  }}
                />
              </div>
              <h2
                style={{
                  color: "#30c18e",
                  fontSize: "2.5rem",
                  fontWeight: "bold",
                  margin: "20px 0 10px",
                }}
              >
                Book A Demo
              </h2>
            </div>

            <div style={{ height: "700px", overflow: "hidden" }}>
              <iframe
                src="https://login.omxsales.com/widget/form/6811c1c5c6a26"
                style={{ width: "100%", height: "100%", border: "none" }}
                title="Book A Demo Form"
              />
            </div>
          </div>
        </motion.div>
      )}
    </>
  );
}
