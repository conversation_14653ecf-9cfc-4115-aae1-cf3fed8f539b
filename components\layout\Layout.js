'use client'

import { useEffect, useState } from "react"
import AddClassBody from '../elements/AddClassBody'
import BackToTop from '../elements/BackToTop'
import Breadcrumb from './Breadcrumb'
import MobileMenu from './MobileMenu'
import MobileMenu1 from './MobileMenu1'
import MobileMenu3 from './MobileMenu3'

import Footer1 from './footer/Footer1'
import Footer2 from './footer/Footer2'
import Footer3 from './footer/Footer3'
import Footer4 from './footer/Footer4'

import Header1 from "./header/Header1"
import Header2 from "./header/Header2"
import Header3 from "./header/Header3"
import Header4 from "./header/Header4"
import Header5 from "./header/Header5"
import Header6 from "./header/Header6"

// Import chatbot components
import ChatbotSales from '../elements/ChatbotSales'
import ChatbotFlow from '../elements/ChatbotFlow'
import ChatbotSync from '../elements/ChatbotSync'

export default function Layout({
    headerStyle,
    footerStyle,
    headTitle,
    breadcrumbTitle,
    children,
    topBar,
    headerCls,
    logoWhite,
    chatbotType,
    mobileMenuStyle
}) {
    const [scroll, setScroll] = useState(0)

    // Mobile Menu
    const [isMobileMenu, setMobileMenu] = useState(false)
    const handleMobileMenu = () => {
        setMobileMenu(!isMobileMenu)
        !isMobileMenu
            ? document.body.classList.add("mobile-menu-active")
            : document.body.classList.remove("mobile-menu-active")
    }

    useEffect(() => {
        const WOW = require('wowjs')
        window.wow = new WOW.WOW({ live: false })
        window.wow.init()

        const handleScroll = () => {
            const scrollCheck = window.scrollY > 100
            setScroll(scrollCheck)
        }

        document.addEventListener("scroll", handleScroll)
        return () => document.removeEventListener("scroll", handleScroll)
    }, [])

    // Render header based on headerStyle
    const renderHeader = () => {
        const commonProps = {
            scroll,
            isMobileMenu,
            handleMobileMenu,
            topBar,
            headerCls,
            logoWhite
        }

        switch (headerStyle) {
            case 1:
                return <Header1 {...commonProps} />
            case 2:
                return <Header2 {...commonProps} />
            case 3:
                return <Header3 {...commonProps} />
            case 4:
                return <Header4 {...commonProps} />
            case 5:
                return <Header5 {...commonProps} />
            case 6:
                return <Header6 {...commonProps} />
            default:
                return <Header1 {...commonProps} />
        }
    }
    // Set document title
    const renderMobileMenu = () => {
    const commonProps = {
        isMobileMenu,
        handleMobileMenu,
    }

    switch (mobileMenuStyle) {
        case 1:
            return <MobileMenu1 {...commonProps} />
        case 2:
            return <MobileMenu3 {...commonProps} />
        default:
            return <MobileMenu {...commonProps} />
    }
}
    // Render footer based on footerStyle
    const renderFooter = () => {
        switch (footerStyle) {
            case 1:
                return <Footer1 />
            case 2:
                return <Footer2 />
            case 3:
                return <Footer3 />
            case 4:
                return <Footer4 />
            default:
                return <Footer1 />
        }
    }

    // Render chatbot based on chatbotType
    const renderChatbot = () => {
        switch (chatbotType) {
            case 1:
                return <ChatbotSales />
            case 2:
                return <ChatbotSync />
            case 3:
                return <ChatbotFlow />
            default:
                return <BackToTop />
        }
    }

    return (
        <>
            <AddClassBody />
            <div className="body-overlay-1" onClick={handleMobileMenu} />

            {renderHeader()}

            {renderMobileMenu()}


            <main className="main">
                {breadcrumbTitle && <Breadcrumb breadcrumbTitle={breadcrumbTitle} />}
                {children}
            </main>

            {renderFooter()}
            {/* <BackToTop /> */}
            {renderChatbot()}
        </>
    )
}
