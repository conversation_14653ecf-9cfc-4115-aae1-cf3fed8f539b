"use client";
import Link from "next/link";
import { useState } from "react";

export default function MobileMenu1({ isMobileMenu, handleMobileMenu }) {
  const [isActive, setIsActive] = useState({
    status: false,
    key: "",
  });

  const handleToggle = (key) => {
    if (isActive.key === key) {
      setIsActive({
        status: false,
      });
    } else {
      setIsActive({
        status: true,
        key,
      });
    }
  };
  return (
    <>
      <div
        className={`mobile-header-active mobile-header-wrapper-style perfect-scrollbar ${
          isMobileMenu ? "sidebar-visible" : ""
        }`}
      >
        <div className="mobile-header-wrapper-inner">
          <div
            className={`burger-icon burger-icon-white ${
              isMobileMenu ? "burger-close" : ""
            }`}
            onClick={handleMobileMenu}
          >
            <span className="burger-icon-top" />
            <span className="burger-icon-mid" />
            <span className="burger-icon-bottom" />
          </div>
          {/* <div className="mobile-header-top">
                        <div className="user-account"><img src="/assets/imgs/page/homepage6/author.png" alt="OMX Digital" />
                            <div className="content">
                                <h6 className="user-name"><PERSON></h6>
                                <p className="font-xs text-muted">You have 4 new messages</p>
                            </div>
                        </div>
                    </div> */}
          <div className="mobile-header-content-area ">
            <div className="perfect-scroll d-flex  flex-column justify-content-between mt-3">
              {/* <div className="mobile-search mobile-header-border mb-30">
                                <form action="#">
                                    <input type="text" placeholder="Search for items…" /><i className="fi-rr-search" />
                                </form>
                            </div> */}
              <div className="mobile-menu-wrap mobile-header-border">
                <nav>
                  <ul className="mobile-menu font-heading ">
                    <li
                      className={
                        isActive.key == 1
                          ? "has-children active"
                          : "has-children"
                      } 
                      onClick={() => handleToggle(1)}
                    >
                      <span
                        className="menu-expand"
                        onClick={() => handleToggle(1)}
                      >
                        <i className="fi-rr-angle-small-down" />
                      </span>
                      <Link className="active" href="">
                        Products
                      </Link>
                      <ul
                        className="sub-menu"
                        style={{
                          display: `${isActive.key == 1 ? "block" : "none"}`,
                        }}
                      >
                        <li>
                          {" "}
                          <Link href="/omx-sales">OMX Sales</Link>{" "}
                        </li>
                        <li>
                          {" "}
                          <Link href="/omx-sync">OMX Sync</Link>{" "}
                        </li>
                        <li>
                          {" "}
                          <Link href="https://omxflow.com/">OMX Flow</Link>{" "}
                        </li>

                        {/* <li><Link href="/">Business Solutions</Link></li>
                                                <li><Link href="/index-2">Marketing App</Link></li>
                                                <li><Link href="/index-3">Web Agency</Link></li>
                                                <li><Link href="/index-4">Digital Agency</Link></li>
                                                <li><Link href="/index-5">3D Products</Link></li>
                                                <li><Link href="/index-6">AI Platform</Link></li>
                                                 */}
                      </ul>
                    </li>
                   <li>
                        <Link href="#features">Features </Link>
                    </li> 
                    {/* <li>
                        <Link href="#plan">Pricing </Link>
                    </li> */}
                    <li>
                        <Link href="#faq">FAQ </Link>
                    </li>
                     
                    <li>
                        <Link href="/about">About </Link>
                    </li>
                    <li>
                        <Link href="/contact">Contact </Link>
                    </li>
                   
                  </ul>
                </nav>
              </div>
              
              <div className="mobile-social-icon mb-50 pb-5">
                <h6 className="mb-25">Follow Us</h6>
                    <Link
                      className="icon-socials icon-facebook"
                      href="https://www.facebook.com/onlinemarketingxchange"
                    >
                      <img
                        alt="OMX Digital"
                        src="/assets/imgs/template/icons/fb.svg"
                      />
                    </Link>
                    <Link
                      className="icon-socials icon-instagram"
                      href="https://www.instagram.com/omxdigital.ai/"
                    >
                      <img
                        alt="OMX Digital"
                        src="/assets/imgs/template/icons/instagram.svg"
                      />
                    </Link>
                    <Link
                      className="icon-socials icon-linkedin"
                      href="https://www.linkedin.com/company/omxdigital/"
                    >
                      <img
                        alt="OMX Digital"
                        src="/assets/imgs/template/icons/in.svg"
                      />
                    </Link>
                    <Link
                      className="icon-socials icon-yt"
                      href="https://www.youtube.com/@OMXDIGITAL"
                    >
                      <img
                        alt="OMX Digital"
                        src="/assets/imgs/template/icons/youtube.svg"
                      />
                    </Link>
                <div className="site-copyright mt-3">
                  Copyright 2025 © OMX Digital.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
