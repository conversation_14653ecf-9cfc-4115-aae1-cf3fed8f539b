import Link from "next/link";
import SearchForm from "./SearchForm";
import WebCTA from "@/components/elements/WebCTA";
export default function Header5({
  scroll,
  isMobileMenu,
  handleMobileMenu,
  topBar,
  headerCls,
  logoWhite,
}) {
  return (
    <>
      <header
        className={`header ${headerCls ? headerCls : ""} sticky-bar ${
          scroll ? "stick" : ""
        }`}
      >
        {topBar && (
          <div className="top-bar">
            <div className="container">
              <div className="top-bar-inner">
                <div className="box-top-bar-left">
                  <span className="address-icon text-md">
                   OMX
                  </span>
                </div>
                <div className="box-top-bar-right">
                  <a className="phone-icon text-md" href="tel:(*************">
                    (*************
                  </a>
                  <a
                    className="email-icon text-md"
                    href="mailto:<EMAIL>"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>
            </div>
          </div>
        )}
        <div className="container">
          <div className="main-header">
            <div className="header-left">
              <div className="header-logo">
                <Link className="d-flex" href="/">
                  <img
                    alt="OMX Digital"
                    src={`/assets/imgs/template/${
                      logoWhite ? "logo-white" : "logo-1"
                    }.png`}
                  />
                </Link>
              </div>
              <div className="header-nav">
                <nav className="nav-main-menu d-none d-xl-block">
                  <ul className="main-menu">
                    <li className="has-children">
                      <Link className="active" href="/">
                        Products
                      </Link>
                      <ul className="sub-menu">
                        <li>
                          <Link href="/omx-sales">OMX Sales</Link>
                        </li>
                        <li>
                          <Link href="/omx-sync">OMX Sync</Link>
                        </li>
                        <li>
                          <Link href="https://omxflow.com/" target="_blank">OMX Flow</Link>
                        </li>
                     
                      </ul>
                    </li>
                    <li className="has-children">
                      <Link className="active" href="#">
                        Services
                      </Link>
                      <ul className="sub-menu">
                        <li>
                          <Link href="/omx-sync">Business Automation</Link>
                        </li>
                        <li>
                          <Link href="/omx-sales">Marketing Automation</Link>
                        </li>
                        <li>
                          <Link href="https://omxflow.com/" target="_blank">Whatsapp Automation</Link>
                        </li>
                        <li>
                          <Link href="/digitalmarketing">Digital Marketing</Link>
                        </li>
                        <li>
                          <Link href="/websitedesigning">Website Designing</Link>
                        </li>
                      </ul>
                    </li>
                    <li className="has-children">
                        <Link className="active" href="/about">About </Link>
                    </li>
                   
                    <li className="has-children">
                      <Link href="/contact">Contact</Link>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
            <div className="header-right">
              {/* <WebCTA/> */}
              {/* <SearchForm /> */}
              <Link className="btn btn-brand-4-medium hover-up" href="/form">
                Book a Demo
                <svg
                  width={22}
                  height={22}
                  viewBox="0 0 22 22"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M22 11.0003L18.4791 7.47949V10.3074H0V11.6933H18.4791V14.5213L22 11.0003Z"
                    fill="true"
                  ></path>
                </svg>
              </Link>
              <div
                className="burger-icon burger-icon-white"
                onClick={handleMobileMenu}
              >
                <span className="burger-icon-top" />
                <span className="burger-icon-mid" />
                <span className="burger-icon-bottom" />
              </div>
            </div>
          </div>
        </div>
      </header>
    </>
  );
}
