import VideoPopup from "@/components/elements/VideoPopup";
import Link from "next/link";
import DigitalCTA from "@/components/elements/DigitalCTA";
export default function Section2() {
  return (
    <>
      <section className="section-box wow animate__animated animate__fadeIn box-what-we-do">
        <div className="container">
          <div className="text-center">
            <Link className="btn btn-brand-5" href="#">
              What do we do?
            </Link>
            <h2 className="heading-1 neutral-0 mt-15 mb-20">
              Enter OMX Growth Engine™
            </h2>
            <p className="text-xl neutral-700 mb-40">
              A Fully Done-For-You Growth Infrastructure—Built Once, Works Forever
            </p>
            <p className="text-lg neutral-700 mb-65">
              We install an entire lead-gen and sales infrastructure into your business in just 7 days.
            </p>
          </div>
          <div className="box-list-we-do">
            <div className="row">
              <div className="col-xl-3 col-lg-5">
                <div className="card-features-6 hover-up">
                  <div className="card-image">
                    <img
                      src="/assets/imgs/page/homepage4/web.png"
                      alt="Omx Digital"
                    />
                  </div>
                  <div className="card-info">
                    <h6>Strategy</h6>
                    <p className="text-sm neutral-400">
                      Comprehensive growth strategy tailored to your business goals and market position
                    </p>
                    <img
                      className="mt-25"
                      src="/assets/imgs/page/homepage4/img-web.png"
                      alt="Omx Digital"
                    />
                  </div>
                </div>
              </div>
              <div className="col-xl-4 col-lg-7">
                <div className="card-features-6 hover-up">
                  <div className="card-image">
                    <img
                      src="/assets/imgs/page/homepage4/global.png"
                      alt="Omx Digital"
                    />
                  </div>
                  <div className="card-info">
                    <h6>Funnel</h6>
                    <p className="text-sm neutral-400">
                      High-converting sales funnels designed to turn visitors into paying customers
                    </p>
                  </div>
                </div>
                <div className="card-features-6 card-arrow-2 hover-up">
                  <div className="card-image">
                    <img
                      src="/assets/imgs/page/homepage4/laptop.png"
                      alt="Omx Digital"
                    />
                  </div>
                  <div className="card-info">
                    <h6>Creatives</h6>
                    <p className="text-sm neutral-400">
                      Scroll-stopping ad creatives and content that captures attention and drives action
                    </p>
                  </div>
                </div>
              </div>
              <div className="col-xl-5 col-lg-12">
                <div className="row">
                  <div className="col-lg-12">
                    <div className="card-features-6 card-arrow-3 hover-up">
                      <div className="card-image">
                        <img
                          src="/assets/imgs/page/homepage4/brain.png"
                          alt="Omx Digital"
                        />
                      </div>
                      <div className="card-info">
                        <div className="card-info-inner">
                          <div className="card-info-left">
                            <h6>Ads</h6>
                            <p className="text-sm neutral-400">
                              Targeted Meta & Google ad campaigns that reach your ideal customers
                            </p>
                          </div>
                          {/* <div className="card-info-right">
                            <Link
                              className="btn btn-learmore-2 neutral-0"
                              href="#"
                            >
                              <span>
                                <svg
                                  width={38}
                                  height={38}
                                  viewBox="0 0 38 38"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <rect
                                    width={38}
                                    height={38}
                                    rx={19}
                                    fill="#2B2C2D"
                                  ></rect>
                                  <g clipPath="url(#clip0_9_2340)">
                                    <path
                                      d="M23.6537 16.8149L14.718 25.7506L13.25 24.2826L22.1847 15.3469H14.31V13.2705H25.7301V24.6906H23.6537V16.8149Z"
                                      fill="#C5FF55"
                                    />
                                  </g>
                                  <defs>
                                    <clipPath id="clip0_9_2340">
                                      <rect
                                        width={13}
                                        height={13}
                                        fill="white"
                                        transform="translate(13 13)"
                                      />
                                    </clipPath>
                                  </defs>
                                </svg>
                              </span>
                              Learn More
                            </Link>
                          </div> */}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-6">
                    <div className="card-features-6 card-features-300 hover-up">
                      <div className="card-image">
                        <img
                          src="/assets/imgs/page/homepage4/search.png"
                          alt="Omx Digital"
                        />
                      </div>
                      <div className="card-info">
                        <h6>Automation</h6>
                        <p className="text-sm neutral-400">
                          WhatsApp & email automation that nurtures leads and converts them 24/7
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-6">
                    <div className="card-features-6 card-features-300 hover-up">
                      <div className="card-image">
                        <img
                          src="/assets/imgs/page/homepage4/cloud.png"
                          alt="Omx Digital"
                        />
                      </div>
                      <div className="card-info">
                        <h6>CRM</h6>
                        <p className="text-sm neutral-400">
                          Complete customer relationship management system to track and close deals
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="text-center mt-50">
            <p className="text-xl neutral-700 mb-40">
              You focus on closing. We handle the rest.
            </p>
          </div>
          <div className="d-flex mb-60 justify-content-center">
                            {/* <DigitalCTA /> */}
            <Link className="btn btn-brand-4-medium hover-up" href="/form">
                                              Book a Demo
                                              <svg
                                                width={22}
                                                height={22}
                                                viewBox="0 0 22 22"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg"
                                              >
                                                <path
                                                  d="M22 11.0003L18.4791 7.47949V10.3074H0V11.6933H18.4791V14.5213L22 11.0003Z"
                                                  fill="true"
                                                ></path>
                                              </svg>
                                </Link>

          </div>
          {/* <div className="box-buttons justify-content-center mt-35">
            <Link className="btn btn-brand-4-medium mr-15" href="#">
              Get Started
              <svg
                width={22}
                height={22}
                viewBox="0 0 22 22"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M22 11.0003L18.4791 7.47949V10.3074H0V11.6933H18.4791V14.5213L22 11.0003Z"
                  fill="true"
                ></path>
              </svg>
            </Link>
            <VideoPopup />
          </div> */}
        </div>
      </section>
    </>
  );
}
