"use client";
import DigitalCTA from "@/components/elements/DigitalCTA";
import Link from "next/link";
export default function Section5() {
  return (
    <>
      <section className="section-box wow animate__animated animate__fadeIn box-case-study-2 box-client-2">
        <div className="container">
          <div className="block-case-study">
            <div className="row align-items-center mb-30">
              <div className="col-lg-7 mb-30">
                <h2 className="heading-1 text-linear-3 mb-35">
                  The Brutal Truth Most Businesses Face
                </h2>
                <div className="truth-list mb-40">
                  <p className="text-lg neutral-0 mb-20">
                    You're spending on ads, but your calendar's still empty.
                  </p>
                  <p className="text-lg neutral-0 mb-20">
                    You hire team and they keep designing and posting which doesn't bring you Value
                  </p>
                  <p className="text-lg neutral-0 mb-20">
                    Leads trickle in—but go cold after 24 hours.
                  </p>
                  <p className="text-lg neutral-0 mb-20">
                    Your team's overwhelmed or undertrained.
                  </p>
                  <p className="text-lg neutral-0 mb-20">
                    There's no follow-up system, no automation, no CRM.
                  </p>
                  <p className="text-lg neutral-0 mb-20">
                    You're stuck in a cycle of spending more and converting less.
                  </p>
                </div>
                <p className="text-lg neutral-0 mb-20 fw-bold">
                  Sound familiar?
                </p>
                <p className="text-lg neutral-0 mb-40 fw-bold">
                  You're not short on leads. You're short on systems.
                </p>
              </div>
              <div className="col-lg-5 mb-30">
                <img
                  src="/assets/imgs/page/premiumservice/dfy_2.png"
                  alt="Omxdigital"
                  className="img-fluid"
                />
              </div>
            </div>
            <div className="d-flex justify-content-center">
              {/* <DigitalCTA/> */}
              <Link className="btn btn-brand-4-medium hover-up" href="/form">
                                              Book a Demo
                                              <svg
                                                width={22}
                                                height={22}
                                                viewBox="0 0 22 22"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg"
                                              >
                                                <path
                                                  d="M22 11.0003L18.4791 7.47949V10.3074H0V11.6933H18.4791V14.5213L22 11.0003Z"
                                                  fill="true"
                                                ></path>
                                              </svg>
                </Link>
              </div>
          </div>
        </div>
      </section>

      <style jsx>{`
        .truth-list p {
          position: relative;
          padding-left: 20px;
        }
        
        .truth-list p::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 8px;
          height: 8px;
          background: linear-gradient(90deg, #FF6B6B, #4ECDC4);
          border-radius: 50%;
        }
        
        .fw-bold {
          font-weight: 600;
        }
      `}</style>
    </>
  );
}
