"use client";
import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";
import DigitalCTA from "@/components/elements/DigitalCTA";
import Link from "next/link";

export default function Section7() {
  // References for scroll detection
  const sectionRef = useRef(null);
  const headerRef = useRef(null);
  const cardsRef = useRef(null);
  
  // Check if elements are in view
  const isSectionInView = useInView(sectionRef, { once: true, amount: 0.1 });
  const isHeaderInView = useInView(headerRef, { once: true, amount: 0.5 });
  const isCardsInView = useInView(cardsRef, { once: true, amount: 0.2 });

  // Animation variants
  const sectionVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        duration: 0.7,
        staggerChildren: 0.3
      } 
    }
  };

  const headerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.6,
        staggerChildren: 0.2
      } 
    }
  };

  const textVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.5 } 
    }
  };

  const cardContainerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.15
      }
    }
  };

  return (
    <>
      <motion.section 
        className="section-box box-our-working"
        ref={sectionRef}
        initial="hidden"
        animate={isSectionInView ? "visible" : "hidden"}
        variants={sectionVariants}
      >
        <div className="container">
          <motion.div 
            className="text-center"
            ref={headerRef}
            variants={headerVariants}
            initial="hidden"
            animate={isHeaderInView ? "visible" : "hidden"}
          >
            <motion.h2 
              className="text-48-semibold neutral-1000 mb-35"
              variants={textVariants}
            >
              What's Included in OMX Growth Engine™
            </motion.h2>
            <motion.p 
              className="text-xl neutral-700"
              variants={textVariants}
            >
              Every component is designed to drive ROI and scale predictably.
            </motion.p>
          </motion.div>
          
          <motion.div 
            className="row mt-65"
            ref={cardsRef}
            variants={cardContainerVariants}
            initial="hidden"
            animate={isCardsInView ? "visible" : "hidden"}
          >
            <WhyUsCard 
              number="1"
              icon="🚀"
              title="Custom Landing Page + Funnel"
              description="Built for one job: capturing qualified, ready-to-talk leads."
              features={[
                "High-converting copy + design",
                "Fast-loading, mobile-optimized",
                "Integrated with your CRM + WhatsApp"
              ]}
              index={0}
              offset={false}
            />
            
            <WhyUsCard 
              number="2"
              icon="🎥"
              title="Scroll-Stopping Creatives & Short Reels"
              description="Capture attention. Drive emotion. Get the click."
              features={[
                "Designed for Meta & Google",
                "Tailored for your niche",
                "Includes captions, hooks, and CTA scripting"
              ]}
              index={1}
              offset={true}
            />
            
            <WhyUsCard 
              number="3"
              icon="📈"
              title="Targeted Meta & Google Ad Campaigns"
              description="We don't 'run ads'—we run strategy."
              features={[
                "Research, build, launch, and optimize",
                "Precise targeting based on buyer intent",
                "Weekly performance tracking + ad refreshes"
              ]}
              index={2}
              offset={false}
            />
            
            <WhyUsCard 
              number="4"
              icon="🤖"
              title="WhatsApp + Email Automation"
              description="Convert leads instantly with zero manual follow-up."
              features={[
                "Auto-response within 30 seconds",
                "Nurturing sequences (reminders, offers, content)",
                "Drip messages pre-built for your business"
              ]}
              index={3}
              offset={true}
            />

            <WhyUsCard 
              number="5"
              icon="💼"
              title="OMX Sales CRM Access"
              description="Track every lead. Assign every follow-up. Never drop the ball."
              features={[
                "Smart lead capture and tagging",
                "Visual pipelines and reminders using Drag and Drop Feature",
                "Custom workflows and reporting"
              ]}
              index={4}
              offset={false}
            />

            <WhyUsCard 
              number="6"
              icon="🧠"
              title="Team Training + Strategy Support"
              description="What's the point of leads if your team can't close?"
              features={[
                "Monthly growth & sales strategy sessions",
                "Team onboarding, scripts & objection handling",
                "Fix what's broken. Double what's working."
              ]}
              index={5}
              offset={true}
            />

            
          </motion.div>
          <div className="d-flex justify-content-center mt-50">
            <Link className="btn btn-brand-4-medium hover-up" href="/form">
                                              Book a Demo
                                              <svg
                                                width={22}
                                                height={22}
                                                viewBox="0 0 22 22"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg"
                                              >
                                                <path
                                                  d="M22 11.0003L18.4791 7.47949V10.3074H0V11.6933H18.4791V14.5213L22 11.0003Z"
                                                  fill="true"
                                                ></path>
                                              </svg>
                                </Link>
            {/* <DigitalCTA/> */}
            </div>
        </div>
      </motion.section>
    </>
  );
}

// Updated card component with features list
function WhyUsCard({ number, icon, title, description, features, index, offset }) {
  const cardRef = useRef(null);
  const isInView = useInView(cardRef, { once: true, amount: 0.2 });
  
  return (
    <div className={`col-lg-4 col-md-6 ${offset ? 'pt-60 mb-30' : ''} .col-sm-6`}>
      <motion.div 
        className="card-working hover-up"
        ref={cardRef}
        initial={{ opacity: 0, y: 50 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
        transition={{ 
          duration: 0.7, 
          delay: index * 0.15,
          type: "spring",
          stiffness: 100
        }}
        whileHover={{ 
          y: -10,
          boxShadow: "0px 10px 25px rgba(0, 0, 0, 0.1)",
          transition: { type: "spring", stiffness: 400, damping: 17 }
        }}
      >
        <motion.div 
          className="card-number"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={isInView ? { scale: 1, opacity: 1 } : { scale: 0.8, opacity: 0 }}
          transition={{ 
            duration: 0.5, 
            delay: 0.3 + index * 0.15,
            type: "spring"
          }}
        >
          <span>{number}</span>
        </motion.div>
        <div className="card-info">
          <motion.h3 
            className="text-22-bold"
            initial={{ opacity: 0, x: -20 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
            transition={{ 
              duration: 0.5, 
              delay: 0.4 + index * 0.15 
            }}
          >
            {title}
          </motion.h3>
          <motion.p 
            className="text-md mb-20"
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : { opacity: 0 }}
            transition={{ 
              duration: 0.5, 
              delay: 0.5 + index * 0.15 
            }}
          >
            {description}
          </motion.p>
          <motion.ul 
            className="feature-list"
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : { opacity: 0 }}
            transition={{ 
              duration: 0.5, 
              delay: 0.6 + index * 0.15 
            }}
          >
            {features.map((feature, i) => (
              <motion.li 
                key={i}
                className="text-sm neutral-700 mb-10"
                initial={{ opacity: 0, x: -10 }}
                animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -10 }}
                transition={{ 
                  duration: 0.3, 
                  delay: 0.7 + (index * 0.15) + (i * 0.1)
                }}
              >
                {feature}
              </motion.li>
            ))}
          </motion.ul>
        </div>
      </motion.div>
    </div>
  );
}