"use client";
import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";
import DigitalCTA from "@/components/elements/DigitalCTA";
import Link from "next/link";

export default function Section7() {
  // References for scroll detection
  const sectionRef = useRef(null);
  const headerRef = useRef(null);
  const cardsRef = useRef(null);
  
  // Check if elements are in view
  const isSectionInView = useInView(sectionRef, { once: true, amount: 0.1 });
  const isHeaderInView = useInView(headerRef, { once: true, amount: 0.5 });
  const isCardsInView = useInView(cardsRef, { once: true, amount: 0.2 });

  // Animation variants
  const sectionVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        duration: 0.7,
        staggerChildren: 0.3
      } 
    }
  };

  const headerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.6,
        staggerChildren: 0.2
      } 
    }
  };

  const textVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.5 } 
    }
  };

  const cardContainerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.15
      }
    }
  };

  // Card animations will be handled individually

  return (
    <>
      <motion.section 
        className="section-box box-our-working"
        ref={sectionRef}
        initial="hidden"
        animate={isSectionInView ? "visible" : "hidden"}
        variants={sectionVariants}
      >
        <div className="container">
          <motion.div 
            className="text-center"
            ref={headerRef}
            variants={headerVariants}
            initial="hidden"
            animate={isHeaderInView ? "visible" : "hidden"}
          >
            <motion.h2 
              className="text-48-semibold neutral-1000 mb-35"
              variants={textVariants}
            >
              Why Choose Us?
            </motion.h2>
            <motion.p 
              className="text-md neutral-700"
              variants={textVariants}
            >
             Looking for a reliable whatsapp automation company in ahmedabad with the best marketing automation services ahmedabad? OMX Digital delivers AI-driven solutions to help local brands grow smarter and faster.
              <br className="d-none d-lg-block" />
            </motion.p>
          </motion.div>
          
          <motion.div 
            className="row mt-65"
            ref={cardsRef}
            variants={cardContainerVariants}
            initial="hidden"
            animate={isCardsInView ? "visible" : "hidden"}
          >
            <WhyUsCard 
              number="1"
              title="Data-Driven Approach"
              description="Every strategy is backed by research, analytics, and real-time performance tracking."
              index={0}
              offset={false}
            />
            
            <WhyUsCard 
              number="2"
              title="Conversion-First Mindset"
              description="We don't just drive traffic—we drive sales and revenue."
              index={1}
              offset={true}
            />
            
            <WhyUsCard 
              number="3"
              title="End-to-End Solutions"
              description="From branding to lead generation, we create a cohesive digital ecosystem that works."
              index={2}
              offset={false}
            />
            
            <WhyUsCard 
              number="4"
              title="Innovative & AI-Powered"
              description="We leverage cutting-edge automation and AI-driven strategies for unmatched efficiency."
              index={3}
              offset={true}
            />
          </motion.div>
          <div className="d-flex justify-content-center">
            <Link className="btn btn-brand-4-medium hover-up" href="/form">
                                                          Book Demo
                                                          <svg
                                                            width={22}
                                                            height={22}
                                                            viewBox="0 0 22 22"
                                                            fill="none"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                          >
                                                            <path
                                                              d="M22 11.0003L18.4791 7.47949V10.3074H0V11.6933H18.4791V14.5213L22 11.0003Z"
                                                              fill="true"
                                                            ></path>
                                                          </svg>
                            </Link>
            {/* <DigitalCTA/> */}
            </div>
        </div>
      </motion.section>
    </>
  );
}

// Extracted card component with its own animations
function WhyUsCard({ number, title, description, index, offset }) {
  const cardRef = useRef(null);
  const isInView = useInView(cardRef, { once: true, amount: 0.2 });
  
  return (
    <div className={`col-lg-3 col-md-6 ${offset ? 'pt-60 mb-30' : ''} .col-sm-6`}>
      <motion.div 
        className="card-working hover-up"
        ref={cardRef}
        initial={{ opacity: 0, y: 50 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
        transition={{ 
          duration: 0.7, 
          delay: index * 0.15,
          type: "spring",
          stiffness: 100
        }}
        whileHover={{ 
          y: -10,
          boxShadow: "0px 10px 25px rgba(0, 0, 0, 0.1)",
          transition: { type: "spring", stiffness: 400, damping: 17 }
        }}
      >
        <motion.div 
          className="card-number"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={isInView ? { scale: 1, opacity: 1 } : { scale: 0.8, opacity: 0 }}
          transition={{ 
            duration: 0.5, 
            delay: 0.3 + index * 0.15,
            type: "spring"
          }}
        >
          <span>{number}</span>
        </motion.div>
        <div className="card-info">
          <motion.h3 
            className="text-22-bold"
            initial={{ opacity: 0, x: -20 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
            transition={{ 
              duration: 0.5, 
              delay: 0.4 + index * 0.15 
            }}
          >
            {title}
          </motion.h3>
          <motion.p 
            className="text-md"
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : { opacity: 0 }}
            transition={{ 
              duration: 0.5, 
              delay: 0.5 + index * 0.15 
            }}
          >
            {description}
          </motion.p>
        </div>
      </motion.div>
    </div>
  );
}