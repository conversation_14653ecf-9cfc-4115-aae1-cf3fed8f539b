'use client'
import Link from 'next/link'
import { useState } from 'react'

export default function Section10() {
    const [isActive, setIsActive] = useState({
        status: false,
        key: 1,
    })

    const faqData = {
  leftColumn: [
    {
      id: 1,
      question: "What services does your WhatsApp automation company in Bangalore offer?",
      answer:
        "We help Bangalore-based businesses automate lead capture, follow-ups, and customer support using WhatsApp flows, AI bots, and API integration—all fully customized to your brand.",
    },
    {
      id: 2,
      question: "Are you the best SEO company in Bangalore for long-term growth?",
      answer:
        "Yes. Our SEO team in Bangalore focuses on sustainable traffic through high-intent keyword strategies, technical SEO, content planning, and analytics—all backed by WhatsApp automation to increase lead conversion.",
    },
    {
      id: 3,
      question: "Do you provide WhatsApp API services in Bangalore for my business?",
      answer:
        "Absolutely. We’re an experienced WhatsApp API provider in Bangalore, enabling your business to send bulk messages, set autoresponders, and trigger real-time chats through automation.",
    },
    {
      id: 4,
      question: "Can your Bangalore team design and develop websites as well?",
      answer:
        "Yes—we’re a full-service website development company in Bangalore. From UI/UX to SEO-optimized web builds, we ensure your online presence is as powerful as your product.",
    },
     {
      id: 5,
      question: "What makes your digital marketing agency in Bangalore different?",
      answer:
        "Our digital marketing strategies are automation-ready. We specialize in SEO, paid ads, and content—paired with WhatsApp and CRM integrations to ensure no leads are lost.",
    },
    {
      id: 6,
      question: "How does AI automation for business work in Bangalore markets?",
      answer:
        "In Bangalore’s fast-paced tech ecosystem, AI automation helps businesses save time and scale. We create intelligent workflows for lead qualification, task delegation, and chatbot interactions.",
    },
  ],
  rightColumn: [
   
    {
      id: 7,
      question: "Can you run both SEO and WhatsApp campaigns together in Bangalore?",
      answer:
        "Yes—we offer blended SEO + WhatsApp automation campaigns for Bangalore clients, so your traffic doesn't just increase—it converts in real time.",
    },
    {
    id: 8,
    question: "Are you a website development company in Bangalore?",
    answer:
      "Yes, we deliver full-stack website development tailored for Bangalore’s fast-moving business environment.",
  },
  {
    id: 9,
    question: "What makes OMX Digital a leading WhatsApp API provider Bangalore?",
    answer:
      "Our industry-recognized API and automation tools simplify customer engagement for all business types.",
  },
  {
    id: 10,
    question: "Do you provide marketing automation services Bangalore businesses need?",
    answer:
      "Absolutely—we streamline marketing operations with AI-enabled tools for local teams.",
  },
  {
    id: 11,
    question: "How does your AI automation for business Bangalore work?",
    answer:
      "Our solutions use the latest artificial intelligence to automate campaigns, analyze data, and improve business efficiency.",
  }
  ],
};


    const handleClick = (key) => {
        if (isActive.key === key) {
            setIsActive({
                status: false,
            })
        } else {
            setIsActive({
                status: true,
                key,
            })
        }
    }

    // Function to render FAQ items
    const renderFAQItem = (item, accordionId) => (
        <div className="accordion-item" key={item.id}>
            <h2 className="accordion-header" id={`flush-heading${item.id}`} onClick={() => handleClick(item.id)}>
                <button 
                    className={isActive.key === item.id ? "accordion-button" : "accordion-button collapsed"}
                    type="button" 
                    data-bs-toggle="collapse" 
                    data-bs-target={`#flush-collapse${item.id}`} 
                    aria-expanded="false" 
                    aria-controls={`flush-collapse${item.id}`}
                >
                    {item.question}
                </button>
            </h2>
            <div 
                className={isActive.key === item.id ? "accordion-collapse collapse show" : "accordion-collapse collapse"} 
                id={`flush-collapse${item.id}`} 
                aria-labelledby={`flush-heading${item.id}`} 
                data-bs-parent={`#${accordionId}`}
            >
                <div className="accordion-body">
                    <p>{item.answer}</p>
                </div>
            </div>
        </div>
    )

    return (
        <>
            <section className="section-box box-faqs">
                <div className="container">
                    <div className="text-center mb-70">
                        <h3 className="heading-2 mb-20">Frequently Asked Questions</h3>
                        
                    </div>
                    <div className="box-2-col-faqs">
                        {/* Left Column */}
                        <div className="faqs-col">
                            <div className="accordion accordion-flush" id="accordionFAQS">
                                {faqData.leftColumn.map(item => renderFAQItem(item, 'accordionFAQS'))}
                            </div>
                        </div>
                        
                        {/* Right Column */}
                        <div className="faqs-col">
                            <div className="accordion accordion-flush" id="accordionFAQS2">
                                {faqData.rightColumn.map(item => renderFAQItem(item, 'accordionFAQS2'))}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}