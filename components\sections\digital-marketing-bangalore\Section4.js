'use client';
import React from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";

export default function Section4() {
  // Create refs for different elements to detect when they come into view
  const [seoImageRef, seoImageInView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [headingRef, headingInView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [descriptionRef, descriptionInView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [subHeadingRef, subHeadingInView] = useInView({ threshold: 0.1, triggerOnce: true });
  
  // Card refs
  const [card1Ref, card1InView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [card2Ref, card2InView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [card3Ref, card3InView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [card4Ref, card4InView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [card5Ref, card5InView] = useInView({ threshold: 0.2, triggerOnce: true });
  
  // Animation variants
  const fadeInUp = {
    hidden: { opacity: 0, y: 50 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };
  
  const imageScale = {
    hidden: { opacity: 0, scale: 0.92 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.8, ease: "easeOut" }
    }
  };
  
  const staggerCards = {
    hidden: { opacity: 0 },
    visible: (i = 0) => ({
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1 * i,
      },
    }),
  };
  
  const cardItem = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, ease: "easeOut" }
    }
  };
  
  const gifAnimation = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { 
        duration: 0.5, 
        ease: "easeOut" 
      }
    }
  };

  return (
    <>
      <section className="section-box box-case-study-2 box-client-2">
        <div className="container">
          <div className="row">
            <div className="col-lg-6 align-items-center mb-40  order-2 order-lg-1">
              <motion.img
                ref={seoImageRef}
                initial="hidden"
                animate={seoImageInView ? "visible" : "hidden"}
                variants={imageScale}
                src="/assets/imgs/page/digitalmarketing/dm_seo.png"
                alt="SEO services Bangalore - Digital marketing search engine optimization"
              />
            </div>
            <div className="col-lg-6 mb-40 order-1 order-lg-2">
              <div className="box-padding-left-50">
                <motion.h2 
                  ref={headingRef}
                  initial="hidden"
                  animate={headingInView ? "visible" : "hidden"}
                  variants={fadeInUp}
                  className="heading-2 mb-20"
                >
                  GET FOUND WHEN IT MATTERS. SEO SERVICES IN BANGALORE THAT TURN CLICKS INTO CLIENTS.

                </motion.h2>
                <motion.p 
                  ref={descriptionRef}
                  initial="hidden"
                  animate={descriptionInView ? "visible" : "hidden"}
                  variants={fadeInUp}
                  className="text-lg neutral-700 mb-40"
                >
                  If your business isn't ranking on Page 1 of Google, you're
                  losing customers to your competitors. We ensure that your
                  website gets the visibility it deserves with strategic SEO
                  practices designed to increase traffic and conversions.
                </motion.p>
                
              </div>
            </div>
            
            
          </div>
          <div className="mt-40">
            <motion.h4
                  ref={subHeadingRef}
                  initial="hidden"
                  animate={subHeadingInView ? "visible" : "hidden"}
                  variants={fadeInUp}
                >
                  What We Do:
                </motion.h4>
                <motion.div 
                  className="row mt-50"
                  variants={staggerCards}
                  initial="hidden"
                  animate="visible"
                >
                  <div className="col-lg-6 col-sm-6">
                    <motion.div 
                      className="card-feature-2"
                      ref={card1Ref}
                      variants={cardItem}
                      initial="hidden"
                      animate={card1InView ? "visible" : "hidden"}
                    >
                      <div className="card-image">
                        <motion.img
                          variants={gifAnimation}
                          src="/assets/imgs/page/digitalmarketing/search.gif"
                          alt="SEO keyword research Bangalore - Search engine optimization strategy"
                        />
                      </div>
                      <motion.div 
                        className="card-info"
                        variants={fadeInUp}
                      >
                        <Link href="#">
                          <h3 className="text-22-bold">
                            Keyword Research & Strategy
                          </h3>
                        </Link>
                        <p className="text-md neutral-700">
                          Identifying the highest-impact keywords that drive
                          real business results.
                        </p>
                      </motion.div>
                    </motion.div>
                  </div>
                  <div className="col-lg-6 col-sm-6">
                    <motion.div 
                      className="card-feature-2"
                      ref={card2Ref}
                      variants={cardItem}
                      initial="hidden"
                      animate={card2InView ? "visible" : "hidden"}
                    >
                      <div className="card-image">
                        <motion.img
                          variants={gifAnimation}
                          src="/assets/imgs/page/digitalmarketing/seoana.gif"
                          alt="On-page SEO Bangalore - Website optimization and analysis"
                        />
                      </div>
                      <motion.div 
                        className="card-info"
                        variants={fadeInUp}
                      >
                        <Link href="#">
                          <h3 className="text-22-bold">On-Page SEO</h3>
                        </Link>
                        <p className="text-md neutral-700">
                          Optimizing website structure, content, and meta tags
                          to improve rankings.
                        </p>
                      </motion.div>
                    </motion.div>
                  </div>
                  <div className="col-lg-6 col-sm-6">
                    <motion.div 
                      className="card-feature-2"
                      ref={card3Ref}
                      variants={cardItem}
                      initial="hidden"
                      animate={card3InView ? "visible" : "hidden"}
                    >
                      <div className="card-image">
                        <motion.img
                          variants={gifAnimation}
                          src="/assets/imgs/page/digitalmarketing/content.gif"
                          alt="Content optimization Bangalore - SEO content strategy and creation"
                        />
                      </div>
                      <motion.div 
                        className="card-info"
                        variants={fadeInUp}
                      >
                        <Link href="#">
                          <h3 className="text-22-bold">Content Marketing </h3>
                        </Link>
                        <p className="text-md neutral-700">
                          SEO-driven blogs, articles, and landing pages that
                          establish authority and drive traffic.
                        </p>
                      </motion.div>
                    </motion.div>
                  </div>
                  <div className="col-lg-6 col-sm-6">
                    <motion.div 
                      className="card-feature-2"
                      ref={card4Ref}
                      variants={cardItem}
                      initial="hidden"
                      animate={card4InView ? "visible" : "hidden"}
                    >
                      <div className="card-image">
                        <motion.img
                          variants={gifAnimation}
                          src="/assets/imgs/page/digitalmarketing/seo2.gif"
                          alt="Technical SEO Bangalore - Website performance optimization"
                        />
                      </div>
                      <motion.div 
                        className="card-info"
                        variants={fadeInUp}
                      >
                        <Link href="#">
                          <h3 className="text-22-bold">Technical SEO</h3>
                        </Link>
                        <p className="text-md neutral-700">
                          Site speed optimization, mobile-friendliness, and
                          performance enhancements for higher search rankings.
                        </p>
                      </motion.div>
                    </motion.div>
                  </div>
                  <div className="col-lg-6 col-sm-6">
                    <motion.div 
                      className="card-feature-2"
                      ref={card5Ref}
                      variants={cardItem}
                      initial="hidden"
                      animate={card5InView ? "visible" : "hidden"}
                    >
                      <div className="card-image">
                        <motion.img
                          variants={gifAnimation}
                          src="/assets/imgs/page/digitalmarketing/localseo.gif"
                          alt="Local SEO Bangalore - Location-based search optimization"
                        />
                      </div>
                      <motion.div 
                        className="card-info"
                        variants={fadeInUp}
                      >
                        <Link href="#">
                          <h3 className="text-22-bold">Local SEO</h3>
                        </Link>
                        <p className="text-md neutral-700">
                          Optimize for Google My Business and location-based
                          searches to capture local customers.
                        </p>
                      </motion.div>
                    </motion.div>
                  </div>
                </motion.div>
            </div>
        </div>
      </section>
    </>
  );
}