'use client';
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import DigitalCTA from "@/components/elements/DigitalCTA";
import Link from "next/link";

export default function Section1() {
  // Create refs for different elements with different thresholds
  const [headingRef, headingInView] = useInView({ threshold: 0.1, triggerOnce: true });
  const [paragraph1Ref, paragraph1InView] = useInView({ threshold: 0.1, triggerOnce: true });
  const [paragraph2Ref, paragraph2InView] = useInView({ threshold: 0.1, triggerOnce: true });
  const [paragraph3Ref, paragraph3InView] = useInView({ threshold: 0.1, triggerOnce: true });
  const [mainImageRef, mainImageInView] = useInView({ threshold: 0.1, triggerOnce: true });
  const [arrowRef, arrowInView] = useInView({ threshold: 0.1, triggerOnce: true });
  const [iconRef, iconInView] = useInView({ threshold: 0.1, triggerOnce: true });

  // Animation variants
  const headingVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.8, 
        ease: "easeOut"
      } 
    }
  };

  const textHighlightVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { 
        duration: 0.5, 
        ease: "easeOut",
        delay: 0.3
      } 
    }
  };

  const paragraphVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.6, 
        ease: "easeOut"
      } 
    }
  };

  const imageContainerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        duration: 0.8,
        staggerChildren: 0.2
      } 
    }
  };

  const mainImageVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { 
        duration: 0.8, 
        ease: "easeOut"
      } 
    }
  };

  const floatingElementVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.5, 
        ease: "easeOut",
        yoyo: Infinity,
        repeatDelay: 2
      }
    }
  };

  const spinningElementVariants = {
    hidden: { opacity: 0, rotate: -20 },
    visible: { 
      opacity: 1, 
      rotate: 0,
      transition: { 
        duration: 0.5, 
        ease: "easeOut",
        yoyo: Infinity,
        repeatDelay: 3
      }
    }
  };

  return (
    <>
      <section className="section-box">
        <div className="banner-hero hero-3">
          <div className="container">
            <div className="banner-inner">
              <div className="">
              <motion.h1
                className="display-2 mb-25"
                initial={{ opacity: 0, y: -50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              > 
                Your Brand Doesn’t Need Just Visibility <br className="d-none d-lg-block" />
                 It Needs Results.<br className="d-none d-lg-block" /> 
                 We’re Delhi’s Top<span className="text-bg-brand-4"> SEO & WhatsApp</span> 
                <br className="d-none d-lg-block" />
               Automation Experts.
              </motion.h1>
              <motion.div
                                initial={{ opacity: 0, y: -50 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.5 }}
                                className="d-flex justify-content-center pb-4"
                              >
                                <Link className="btn btn-brand-5 ps-4 " href="#">
                                  At OMX Digital Pvt. Ltd.
                                </Link>
                   </motion.div>
                {/* <motion.h1 
                  ref={headingRef}
                  initial="hidden"
                  animate={headingInView ? "visible" : "hidden"}
                  variants={headingVariants}
                  className="display-2 mb-30 mt-25"
                >
                  DIGITAL MARKETING IS NOT{" "}
                  <motion.span 
                    variants={textHighlightVariants} 
                    className="text-96"
                  >
                    JUST ABOUT
                  </motion.span> HOW YOUR SOCIALS
                  LOOK, IT'S ABOUT{" "}
                  <motion.span 
                    variants={textHighlightVariants}
                    className="text-86"
                  >
                    RESULTS.
                  </motion.span>
                </motion.h1> */}
                
                <motion.p 
                  ref={paragraph1Ref}
                  initial="hidden"
                  animate={paragraph1InView ? "visible" : "hidden"}
                  variants={paragraphVariants}
                  className="text-lg neutral-500 mb-55"
                >
                   WhatsApp Automation & SEO Company in Delhi
                  <br />
                  <br />
                  <motion.span
                    ref={paragraph2Ref}
                    initial="hidden"
                    animate={paragraph2InView ? "visible" : "hidden"}
                    variants={paragraphVariants}
                  >
                    OMX Digital is your go-to partner in Delhi for WhatsApp automation, AI-powered lead generation, and result-driven digital marketing. As a top WhatsApp API provider in Delhi, we specialize in scaling your business through automation, SEO, and web solutions.
                  </motion.span>
                  <br /> <br />
                  <motion.span
                    ref={paragraph3Ref}
                    initial="hidden"
                    animate={paragraph3InView ? "visible" : "hidden"}
                    variants={paragraphVariants}
                  >
                    Whether you're launching a new brand, scaling an existing one,
                    or repositioning yourself in the market, we build a
                    full-fledged digital ecosystem that ensures every
                    touchpoint—your website, social media, ads, and content—works
                    together to attract, engage, and convert.
                  </motion.span>
                </motion.p>
                <div className="d-flex mb-60 justify-content-center">
                  {/* <DigitalCTA /> */}
                  <Link className="btn btn-brand-4-medium hover-up" href="/form">
                                                                Book Demo
                                                                <svg
                                                                  width={22}
                                                                  height={22}
                                                                  viewBox="0 0 22 22"
                                                                  fill="none"
                                                                  xmlns="http://www.w3.org/2000/svg"
                                                                >
                                                                  <path
                                                                    d="M22 11.0003L18.4791 7.47949V10.3074H0V11.6933H18.4791V14.5213L22 11.0003Z"
                                                                    fill="true"
                                                                  ></path>
                                                                </svg>
                                                  </Link>
                </div>
              </div>
              <motion.div 
                className=""
                initial="hidden"
                animate={mainImageInView ? "visible" : "hidden"}
                variants={imageContainerVariants}
              >
                <div className="box-images-banner-6">
                  <motion.span 
                    ref={arrowRef}
                    variants={floatingElementVariants}
                    className="animate-1"
                  >
                    {/* <img
                      src="/assets/imgs/page/homepage4/banner-arrow.png"
                      alt="Omxdigital"
                    /> */}
                  </motion.span>
                  <motion.span 
                    ref={iconRef}
                    variants={spinningElementVariants}
                    className="animate-2"
                  >
                    {/* <img
                      src="/assets/imgs/page/homepage4/banner-icon.png"
                      alt="Omxdigital"
                    /> */}
                  </motion.span>
                  <motion.img
                    ref={mainImageRef}
                    variants={mainImageVariants}
                    src="/assets/imgs/page/digitalmarketing/digital_top (1).png"
                    alt="Digital marketing agency in Delhi - OMX Digital comprehensive digital marketing services"
                  />
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}