'use client'
import Link from 'next/link'
import { useState } from 'react'

export default function Section10() {
    const [isActive, setIsActive] = useState({
        status: false,
        key: 1,
    })

    const faqData = {
  leftColumn: [
    {
      id: 1,
      question: "Are you a WhatsApp API provider Delhi businesses can rely on?",
      answer:
        "Yes, OMX Digital provides complete WhatsApp API integration, automation, and campaign tracking for Delhi-based businesses.",
      
    },
    {
      id: 2,
      question: "Can you handle SEO services in Delhi alongside WhatsApp automation?",
      answer:
        "Absolutely. As a top SEO services provider in Delhi, we align our technical, on-page, and local SEO strategies with WhatsApp automation efforts to drive qualified traffic and instant engagement.",
    },
    {
      id: 3,
      question: "Are you both an SEO company and a WhatsApp API provider in Delhi?",
      answer:
        "Yes—we combine the roles of best SEO company in Delhi and trusted WhatsApp API provider, delivering holistic solutions that rank your site, automate your messaging, and nurture leads—all in one place.",
    },
    {
      id: 4,
      question: "Looking for a website design company Delhi trusts?",
      answer:
        "We create modern, responsive, and visually compelling websites tailored for Delhi's competitive market.",
      
    },
  ],
  rightColumn: [
    {
      id: 5,
      question: "What makes your WhatsApp automation company in Delhi different?",
      answer:
        "We’re Delhi-based and results-first: our AI-powered WhatsApp automations—from auto-responders to drip sequences—are built to support SEO-led campaigns and maximize conversions.",
    },
    {
      id: 6,
      question: "Do you offer website development company services in Delhi?",
      answer:
        "Absolutely, our Delhi team specializes in high-conversion, SEO-optimized website development for brands of all sizes.",
    },
    {
      id: 7,
      question: "I need help building a website—do you offer web design and development in Delhi?",
      answer:
        "Yes, as a leading website development company in Delhi, we design mobile‑optimized, SEO-ready websites integrated with WhatsApp automations—so your site not only looks great but also converts leads from day one.",
    },
  ],
};



    const handleClick = (key) => {
        if (isActive.key === key) {
            setIsActive({
                status: false,
            })
        } else {
            setIsActive({
                status: true,
                key,
            })
        }
    }

    // Function to render FAQ items
    const renderFAQItem = (item, accordionId) => (
        <div className="accordion-item" key={item.id}>
            <h2 className="accordion-header" id={`flush-heading${item.id}`} onClick={() => handleClick(item.id)}>
                <button 
                    className={isActive.key === item.id ? "accordion-button" : "accordion-button collapsed"}
                    type="button" 
                    data-bs-toggle="collapse" 
                    data-bs-target={`#flush-collapse${item.id}`} 
                    aria-expanded="false" 
                    aria-controls={`flush-collapse${item.id}`}
                >
                    {item.question}
                </button>
            </h2>
            <div 
                className={isActive.key === item.id ? "accordion-collapse collapse show" : "accordion-collapse collapse"} 
                id={`flush-collapse${item.id}`} 
                aria-labelledby={`flush-heading${item.id}`} 
                data-bs-parent={`#${accordionId}`}
            >
                <div className="accordion-body">
                    <p>{item.answer}</p>
                </div>
            </div>
        </div>
    )

    return (
        <>
            <section className="section-box box-faqs">
                <div className="container">
                    <div className="text-center mb-70">
                        <h3 className="heading-2 mb-20">Frequently Asked Questions</h3>
                        
                    </div>
                    <div className="box-2-col-faqs">
                        {/* Left Column */}
                        <div className="faqs-col">
                            <div className="accordion accordion-flush" id="accordionFAQS">
                                {faqData.leftColumn.map(item => renderFAQItem(item, 'accordionFAQS'))}
                            </div>
                        </div>
                        
                        {/* Right Column */}
                        <div className="faqs-col">
                            <div className="accordion accordion-flush" id="accordionFAQS2">
                                {faqData.rightColumn.map(item => renderFAQItem(item, 'accordionFAQS2'))}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}