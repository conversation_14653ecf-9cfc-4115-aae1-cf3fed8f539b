'use client'
import Link from 'next/link'
import { useState } from 'react'

export default function Section10() {
    const [isActive, setIsActive] = useState({
        status: false,
        key: 1,
    })

const faqData = {
  leftColumn: [
    {
      id: 1,
      question: "Why choose OMX Digital as your WhatsApp automation company in Kolkata and WhatsApp API provider Kolkata?",
      answer:
        "OMX Digital is a trusted WhatsApp automation company in Kolkata and certified WhatsApp API provider Kolkata offering powerful automation, chatbot integration, and seamless WhatsApp communication solutions tailored for Kolkata businesses to engage customers effectively.",
    },
    {
      id: 2,
      question: "How does OMX Digital support businesses as a digital marketing agency in Kolkata and best SEO company in Kolkata?",
      answer:
        "As a full-service digital marketing agency in Kolkata and one of the best SEO companies in Kolkata, OMX Digital delivers targeted marketing strategies, SEO optimization, and AI-driven campaigns that boost Kolkata businesses' online presence and drive quality leads.",
    },
    {
      id: 3,
      question: "What website services does OMX Digital offer as a website development company in Kolkata and website design company?",
      answer:
        "OMX Digital, a leading website development company in Kolkata, creates responsive, user-friendly, and SEO-optimized websites. As a top website design company in Kolkata, we craft visually appealing websites tailored to local business needs that increase conversions.",
    },
    
  ],
  rightColumn: [
    
    {
      id: 4,
      question: "What marketing automation and AI services does OMX Digital provide in Kolkata?",
      answer:
        "Our expert team offers comprehensive marketing automation services Kolkata and advanced AI automation for business Kolkata, including drip campaigns, lead nurturing, CRM automation, and AI-powered insights to help businesses automate workflows and enhance customer engagement.",
    },
    {
      id: 5,
      question: "How can I start working with OMX Digital, the trusted digital marketing agency and WhatsApp automation company in Kolkata?",
      answer:
        "Getting started with OMX Digital is easy! Whether you need a digital marketing agency in Kolkata, WhatsApp automation company in Kolkata, or website development company in Kolkata, contact us for a personalized strategy to grow your business efficiently using the latest AI and automation technology.",
    },
  ],
};



    const handleClick = (key) => {
        if (isActive.key === key) {
            setIsActive({
                status: false,
            })
        } else {
            setIsActive({
                status: true,
                key,
            })
        }
    }

    // Function to render FAQ items
    const renderFAQItem = (item, accordionId) => (
        <div className="accordion-item" key={item.id}>
            <h2 className="accordion-header" id={`flush-heading${item.id}`} onClick={() => handleClick(item.id)}>
                <button 
                    className={isActive.key === item.id ? "accordion-button" : "accordion-button collapsed"}
                    type="button" 
                    data-bs-toggle="collapse" 
                    data-bs-target={`#flush-collapse${item.id}`} 
                    aria-expanded="false" 
                    aria-controls={`flush-collapse${item.id}`}
                >
                    {item.question}
                </button>
            </h2>
            <div 
                className={isActive.key === item.id ? "accordion-collapse collapse show" : "accordion-collapse collapse"} 
                id={`flush-collapse${item.id}`} 
                aria-labelledby={`flush-heading${item.id}`} 
                data-bs-parent={`#${accordionId}`}
            >
                <div className="accordion-body">
                    <p>{item.answer}</p>
                </div>
            </div>
        </div>
    )

    return (
        <>
            <section className="section-box box-faqs">
                <div className="container">
                    <div className="text-center mb-70">
                        <h3 className="heading-2 mb-20">Frequently Asked Questions</h3>
                        
                    </div>
                    <div className="box-2-col-faqs">
                        {/* Left Column */}
                        <div className="faqs-col">
                            <div className="accordion accordion-flush" id="accordionFAQS">
                                {faqData.leftColumn.map(item => renderFAQItem(item, 'accordionFAQS'))}
                            </div>
                        </div>
                        
                        {/* Right Column */}
                        <div className="faqs-col">
                            <div className="accordion accordion-flush" id="accordionFAQS2">
                                {faqData.rightColumn.map(item => renderFAQItem(item, 'accordionFAQS2'))}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}