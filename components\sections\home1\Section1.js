"use client";
import Link from "next/link";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";

export default function Section1() {
  // Create refs for different elements to trigger animations when in view
  const [heroRef, heroInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [titleRef, titleInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [paragraphRef, paragraphInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [paragraph2Ref, paragraph2InView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [listRef, listInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [bgRef, bgInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.8 } },
  };

  const slideUp = {
    hidden: { y: 50, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { duration: 0.6 } },
  };

  const staggerListItems = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const listItemVariant = {
    hidden: { x: -20, opacity: 0 },
    visible: { x: 0, opacity: 1, transition: { duration: 0.4 } },
  };

  const scaleUp = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: { scale: 1, opacity: 1, transition: { duration: 0.6 } },
  };

  return (
    <>
      <section className="section-box" ref={heroRef}>
        <div className="banner-hero hero-5">
          <motion.div
            className="banner-image-main"
            initial="hidden"
            animate={bgInView ? "visible" : "hidden"}
            variants={fadeIn}
            ref={bgRef}
          >
            <motion.div
              className="img-bg"
              initial={{ opacity: 0, scale: 1.1 }}
              animate={
                bgInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 1.1 }
              }
              transition={{ duration: 1.2 }}
            />
            <motion.div
              className="blur-bg blur-move"
              animate={{
                x: [0, 15, 0],
                y: [0, 10, 0],
              }}
              transition={{
                repeat: Infinity,
                duration: 8,
                ease: "easeInOut",
              }}
            />
          </motion.div>
          <div className="banner-inner-top">
            <div className="container">
              <div className="box-banner-left">
                <motion.div
                  initial="hidden"
                  animate={heroInView ? "visible" : "hidden"}
                  variants={scaleUp}
                >
                  <Link className="btn btn-brand-5-new ps-4" href="#">
                    At OMX Digital Pvt. Ltd.
                  </Link>
                </motion.div>

                <motion.h1
                  className="display-2 mb-30 mt-25 neutral-0"
                  ref={titleRef}
                  initial="hidden"
                  animate={titleInView ? "visible" : "hidden"}
                  variants={slideUp}
                >
                  We Don't Just Build Tools, We Automate Growth.
                </motion.h1>

                <motion.p
                  className="text-lg neutral-500 mb-20"
                  ref={paragraphRef}
                  initial="hidden"
                  animate={paragraphInView ? "visible" : "hidden"}
                  variants={fadeIn}
                >
                  In a world where time is money, OMX Digital helps businesses
                  automate, optimize, and scale with AI-powered SaaS solutions
                  and full-stack digital marketing services.
                </motion.p>

                <motion.p
                  className="text-lg neutral-500 mb-20"
                  ref={paragraph2Ref}
                  initial="hidden"
                  animate={paragraph2InView ? "visible" : "hidden"}
                  variants={fadeIn}
                >
                  Whether you're a startup, SME, or enterprise—we create systems
                  that run your operations, drive your marketing, and close your
                  sales… so you can focus on what truly matters: innovation,
                  growth, and customer experience.
                </motion.p>

                <motion.ul
                  className="text-lg neutral-500 mb-55"
                  ref={listRef}
                  initial="hidden"
                  animate={listInView ? "visible" : "hidden"}
                  variants={staggerListItems}
                >
                  <motion.li variants={listItemVariant}>
                    AI + Automation First
                  </motion.li>
                  <motion.li variants={listItemVariant}>
                    Growth-Driven by Design
                  </motion.li>
                  <motion.li variants={listItemVariant}>
                    Everything Working Together, Seamlessly
                  </motion.li>
                </motion.ul>

                {/* Commented out as per original code
                <div className="d-flex mb-60">
                  <Link className="hover-up mr-5" href="#">
                    <img
                      src="/assets/imgs/page/homepage1/googleplay.png"
                      alt="Nivia"
                    />
                  </Link>
                  <Link className="hover-up" href="#">
                    <img
                      src="/assets/imgs/page/homepage1/appstore.png"
                      alt="Nivia"
                    />
                  </Link>
                </div> */}
              </div>
            </div>
          </div>
          <div className="banner-inner-bottom">
            <div className="container">
              {/* Commented out as per original code
              <div className="box-joined">
                <div className="box-authors">
                  <span className="item-author">
                    <img
                      src="/assets/imgs/page/homepage1/author.png"
                      alt="Nivia"
                    />
                  </span>
                  <span className="item-author">
                    <img
                      src="/assets/imgs/page/homepage1/author2.png"
                      alt="Nivia"
                    />
                  </span>
                  <span className="item-author">
                    <img
                      src="/assets/imgs/page/homepage1/author3.png"
                      alt="Nivia"
                    />
                  </span>
                  <span className="item-author">
                    <span className="text-num-author text-md-bold color-brand-2">
                      +2k
                    </span>
                  </span>
                </div>
                <span className="text-lg d-inline-block">
                  Join thousands of users in using
                  <br className="d-none d-md-block" />
                  the Nivia platform!
                </span>
              </div> */}
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
