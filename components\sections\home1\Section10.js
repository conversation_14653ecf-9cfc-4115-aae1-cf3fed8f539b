
import Link from 'next/link'

export default function Section10() {
    return (
        <>

            <section className="section-box box-pricing-2 box-pricing-3">
                <div className="container">
                    <div className="text-center"> <Link className="btn btn-brand-4-sm" href="#">Pricing Plan</Link>
                        <h2 className="mb-20 mt-15">Ready to Get Started? Don't Worry,<br className="d-none d-lg-block" /> We'll Keep
                            You Under Budget</h2>
                        <p className="text-lg neutral-500 mb-65">Get started with a 5-day trial, 25% off for Yearly Plan, Cancel
                            anytime.</p>
                    </div>
                    <div className="block-pricing">
                        <div className="row">
                            <div className="col-lg-4 col-md-6">
                                <div className="card-pricing card-pricing-style-2 card-pricing-style-3">
                                    <div className="card-title">
                                        <h6>Basic</h6>
                                    </div>
                                    <div className="card-price">
                                        <div className="for-month">
                                            <h1 className="heading-1">$99</h1><span className="text-20-medium color-grey">per
                                                monthly</span>
                                        </div>
                                        <div className="for-year">
                                            <h1 className="heading-1">$1,188</h1><span className="text-20-medium color-grey">yearly</span>
                                        </div>
                                    </div>
                                    <div className="card-button"><Link className="btn btn-get-started" href="#">Get started
                                        <svg width={23} height={8} viewBox="0 0 23 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M22.5 3.99934L18.9791 0.478516V3.30642H0.5V4.69236H18.9791V7.52031L22.5 3.99934Z" fill="true" />
                                        </svg></Link></div>
                                    <div className="card-lists"><strong className="text-18-bold">What’s included</strong>
                                        <ul className="list-feature">
                                            <li>
                                                <svg xmlns="http://www.w3.org/2000/svg" width={26} height={26} viewBox="0 0 26 26" fill="none">
                                                    <g clipPath="url(#clip0_51_57)">
                                                        <path d="M13 26C20.1799 26 26 20.1799 26 13C26 5.8201 20.1799 0 13 0C5.8201 0 0 5.8201 0 13C0 20.1799 5.8201 26 13 26Z" fill="#C5FF55" />
                                                        <path d="M7.11719 13.8396L10.479 17.2014L18.8835 8.79688" stroke="#191919" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_51_57">
                                                            <rect width={26} height={26} fill="white" />
                                                        </clipPath>
                                                    </defs>
                                                </svg> 5,000 Monthly Word Limit
                                            </li>
                                            <li>
                                                <svg xmlns="http://www.w3.org/2000/svg" width={26} height={26} viewBox="0 0 26 26" fill="none">
                                                    <g clipPath="url(#clip0_51_57)">
                                                        <path d="M13 26C20.1799 26 26 20.1799 26 13C26 5.8201 20.1799 0 13 0C5.8201 0 0 5.8201 0 13C0 20.1799 5.8201 26 13 26Z" fill="#C5FF55" />
                                                        <path d="M7.11719 13.8396L10.479 17.2014L18.8835 8.79688" stroke="#191919" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_51_57">
                                                            <rect width={26} height={26} fill="white" />
                                                        </clipPath>
                                                    </defs>
                                                </svg> 50+ Languages
                                            </li>
                                            <li>
                                                <svg xmlns="http://www.w3.org/2000/svg" width={26} height={26} viewBox="0 0 26 26" fill="none">
                                                    <g clipPath="url(#clip0_51_57)">
                                                        <path d="M13 26C20.1799 26 26 20.1799 26 13C26 5.8201 20.1799 0 13 0C5.8201 0 0 5.8201 0 13C0 20.1799 5.8201 26 13 26Z" fill="#C5FF55" />
                                                        <path d="M7.11719 13.8396L10.479 17.2014L18.8835 8.79688" stroke="#191919" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_51_57">
                                                            <rect width={26} height={26} fill="white" />
                                                        </clipPath>
                                                    </defs>
                                                </svg> Advance Editor Tool
                                            </li>
                                            <li>
                                                <svg xmlns="http://www.w3.org/2000/svg" width={26} height={26} viewBox="0 0 26 26" fill="none">
                                                    <g clipPath="url(#clip0_51_57)">
                                                        <path d="M13 26C20.1799 26 26 20.1799 26 13C26 5.8201 20.1799 0 13 0C5.8201 0 0 5.8201 0 13C0 20.1799 5.8201 26 13 26Z" fill="#C5FF55" />
                                                        <path d="M7.11719 13.8396L10.479 17.2014L18.8835 8.79688" stroke="#191919" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_51_57">
                                                            <rect width={26} height={26} fill="white" />
                                                        </clipPath>
                                                    </defs>
                                                </svg> 50 Accounts
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div className="col-lg-4 col-md-6">
                                <div className="card-pricing card-pricing-style-2 card-pricing-style-3 card-pricing-popular"><Link className="btn btn-popular" href="#">Popular</Link>
                                    <div className="card-title">
                                        <h6>Professional</h6>
                                    </div>
                                    <div className="card-price">
                                        <div className="for-month">
                                            <h1 className="heading-1">$199</h1><span className="text-20-medium color-grey">per
                                                monthly</span>
                                        </div>
                                        <div className="for-year">
                                            <h1 className="heading-1">$2,388</h1><span className="text-20-medium color-grey">yearly</span>
                                        </div>
                                    </div>
                                    <div className="card-button"><Link className="btn btn-get-started" href="#">Get started
                                        <svg width={23} height={8} viewBox="0 0 23 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M22.5 3.99934L18.9791 0.478516V3.30642H0.5V4.69236H18.9791V7.52031L22.5 3.99934Z" fill="true" />
                                        </svg></Link></div>
                                    <div className="card-lists"><strong className="text-18-bold">What’s included</strong>
                                        <ul className="list-feature">
                                            <li>
                                                <svg xmlns="http://www.w3.org/2000/svg" width={26} height={26} viewBox="0 0 26 26" fill="none">
                                                    <g clipPath="url(#clip0_51_57)">
                                                        <path d="M13 26C20.1799 26 26 20.1799 26 13C26 5.8201 20.1799 0 13 0C5.8201 0 0 5.8201 0 13C0 20.1799 5.8201 26 13 26Z" fill="#C5FF55" />
                                                        <path d="M7.11719 13.8396L10.479 17.2014L18.8835 8.79688" stroke="#191919" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_51_57">
                                                            <rect width={26} height={26} fill="white" />
                                                        </clipPath>
                                                    </defs>
                                                </svg> 5,000 Monthly Word Limit
                                            </li>
                                            <li>
                                                <svg xmlns="http://www.w3.org/2000/svg" width={26} height={26} viewBox="0 0 26 26" fill="none">
                                                    <g clipPath="url(#clip0_51_57)">
                                                        <path d="M13 26C20.1799 26 26 20.1799 26 13C26 5.8201 20.1799 0 13 0C5.8201 0 0 5.8201 0 13C0 20.1799 5.8201 26 13 26Z" fill="#C5FF55" />
                                                        <path d="M7.11719 13.8396L10.479 17.2014L18.8835 8.79688" stroke="#191919" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_51_57">
                                                            <rect width={26} height={26} fill="white" />
                                                        </clipPath>
                                                    </defs>
                                                </svg> 50+ Languages
                                            </li>
                                            <li>
                                                <svg xmlns="http://www.w3.org/2000/svg" width={26} height={26} viewBox="0 0 26 26" fill="none">
                                                    <g clipPath="url(#clip0_51_57)">
                                                        <path d="M13 26C20.1799 26 26 20.1799 26 13C26 5.8201 20.1799 0 13 0C5.8201 0 0 5.8201 0 13C0 20.1799 5.8201 26 13 26Z" fill="#C5FF55" />
                                                        <path d="M7.11719 13.8396L10.479 17.2014L18.8835 8.79688" stroke="#191919" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_51_57">
                                                            <rect width={26} height={26} fill="white" />
                                                        </clipPath>
                                                    </defs>
                                                </svg> Advance Editor Tool
                                            </li>
                                            <li>
                                                <svg xmlns="http://www.w3.org/2000/svg" width={26} height={26} viewBox="0 0 26 26" fill="none">
                                                    <g clipPath="url(#clip0_51_57)">
                                                        <path d="M13 26C20.1799 26 26 20.1799 26 13C26 5.8201 20.1799 0 13 0C5.8201 0 0 5.8201 0 13C0 20.1799 5.8201 26 13 26Z" fill="#C5FF55" />
                                                        <path d="M7.11719 13.8396L10.479 17.2014L18.8835 8.79688" stroke="#191919" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_51_57">
                                                            <rect width={26} height={26} fill="white" />
                                                        </clipPath>
                                                    </defs>
                                                </svg> 50 Accounts
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div className="col-lg-4 col-md-6">
                                <div className="card-pricing card-pricing-style-2 card-pricing-style-3">
                                    <div className="card-title">
                                        <h6>Enterprise</h6>
                                    </div>
                                    <div className="card-price">
                                        <div className="for-month">
                                            <h1 className="heading-1">$399</h1><span className="text-20-medium color-grey">per
                                                monthly</span>
                                        </div>
                                        <div className="for-year">
                                            <h1 className="heading-1">$4,788</h1><span className="text-20-medium color-grey">per
                                                yearly</span>
                                        </div>
                                    </div>
                                    <div className="card-button"><Link className="btn btn-get-started" href="#">Get started
                                        <svg width={23} height={8} viewBox="0 0 23 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M22.5 3.99934L18.9791 0.478516V3.30642H0.5V4.69236H18.9791V7.52031L22.5 3.99934Z" fill="true" />
                                        </svg></Link></div>
                                    <div className="card-lists"><strong className="text-18-bold">What’s included</strong>
                                        <ul className="list-feature">
                                            <li>
                                                <svg xmlns="http://www.w3.org/2000/svg" width={26} height={26} viewBox="0 0 26 26" fill="none">
                                                    <g clipPath="url(#clip0_51_57)">
                                                        <path d="M13 26C20.1799 26 26 20.1799 26 13C26 5.8201 20.1799 0 13 0C5.8201 0 0 5.8201 0 13C0 20.1799 5.8201 26 13 26Z" fill="#C5FF55" />
                                                        <path d="M7.11719 13.8396L10.479 17.2014L18.8835 8.79688" stroke="#191919" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_51_57">
                                                            <rect width={26} height={26} fill="white" />
                                                        </clipPath>
                                                    </defs>
                                                </svg> 5,000 Monthly Word Limit
                                            </li>
                                            <li>
                                                <svg xmlns="http://www.w3.org/2000/svg" width={26} height={26} viewBox="0 0 26 26" fill="none">
                                                    <g clipPath="url(#clip0_51_57)">
                                                        <path d="M13 26C20.1799 26 26 20.1799 26 13C26 5.8201 20.1799 0 13 0C5.8201 0 0 5.8201 0 13C0 20.1799 5.8201 26 13 26Z" fill="#C5FF55" />
                                                        <path d="M7.11719 13.8396L10.479 17.2014L18.8835 8.79688" stroke="#191919" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_51_57">
                                                            <rect width={26} height={26} fill="white" />
                                                        </clipPath>
                                                    </defs>
                                                </svg> 50+ Languages
                                            </li>
                                            <li>
                                                <svg xmlns="http://www.w3.org/2000/svg" width={26} height={26} viewBox="0 0 26 26" fill="none">
                                                    <g clipPath="url(#clip0_51_57)">
                                                        <path d="M13 26C20.1799 26 26 20.1799 26 13C26 5.8201 20.1799 0 13 0C5.8201 0 0 5.8201 0 13C0 20.1799 5.8201 26 13 26Z" fill="#C5FF55" />
                                                        <path d="M7.11719 13.8396L10.479 17.2014L18.8835 8.79688" stroke="#191919" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_51_57">
                                                            <rect width={26} height={26} fill="white" />
                                                        </clipPath>
                                                    </defs>
                                                </svg> Advance Editor Tool
                                            </li>
                                            <li>
                                                <svg xmlns="http://www.w3.org/2000/svg" width={26} height={26} viewBox="0 0 26 26" fill="none">
                                                    <g clipPath="url(#clip0_51_57)">
                                                        <path d="M13 26C20.1799 26 26 20.1799 26 13C26 5.8201 20.1799 0 13 0C5.8201 0 0 5.8201 0 13C0 20.1799 5.8201 26 13 26Z" fill="#C5FF55" />
                                                        <path d="M7.11719 13.8396L10.479 17.2014L18.8835 8.79688" stroke="#191919" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_51_57">
                                                            <rect width={26} height={26} fill="white" />
                                                        </clipPath>
                                                    </defs>
                                                </svg> 50 Accounts
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}
