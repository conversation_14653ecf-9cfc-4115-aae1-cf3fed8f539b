"use client";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";

export default function Section4() {
  // Create refs for different section elements to trigger animations
  const [sectionRef, sectionInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [headingRef, headingInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [subheadingRef, subheadingInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [paragraphRef, paragraphInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Animation variants
  const fadeInUpVariant = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  };

  const fadeInVariant = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.8 } },
  };

  const scaleVariant = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: { opacity: 1, scale: 1, transition: { duration: 0.5 } },
  };

  return (
    <>
      <motion.section
        className="pt-10 pb-10"
        ref={sectionRef}
        initial="hidden"
        animate={sectionInView ? "visible" : "hidden"}
        variants={fadeInVariant}
      >
        <div className="container">
          <div className="text-center">
            <motion.h2
              className="mb-15"
              ref={headingRef}
              initial="hidden"
              animate={headingInView ? "visible" : "hidden"}
              variants={fadeInUpVariant}
            >
              What We Believe
            </motion.h2>
            <div className="text-lg neutral-700 mb-80">
              <motion.h6
                ref={subheadingRef}
                initial="hidden"
                animate={subheadingInView ? "visible" : "hidden"}
                variants={scaleVariant}
              >
                Automation should be simple. AI should be accessible. Results
                should be measurable.
              </motion.h6>
              <br />
              <motion.p
                ref={paragraphRef}
                initial="hidden"
                animate={paragraphInView ? "visible" : "hidden"}
                variants={fadeInUpVariant}
              >
                We exist to eliminate chaos in business by transforming
                outdated, manual processes into intelligent, automated systems
                that actually work.
              </motion.p>
            </div>
          </div>
          {/* Commented out section as per the original code
                    <div className="row mt-90">
                        <div className="col-lg-4 col-md-6">
                            <div className="card-preparing">
                                <div className="card-image"><img className="wow fadeInUp" src="/assets/imgs/page/homepage1/img-prepare.png" alt="Nivia" /></div>
                                <div className="card-info">
                                    <h5>Easy Control Panel</h5>
                                    <p className="text-lg neutral-700 w-85 mx-auto">Mastering Your Domain with Effortless
                                        Control: Elevate Your Management Experience to New Heights of Ease and Efficiency.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="col-lg-4 col-md-6">
                            <div className="card-preparing">
                                <div className="card-image"><img className="wow fadeInUp" src="/assets/imgs/page/homepage1/img-prepare2.png" alt="Nivia" /></div>
                                <div className="card-info">
                                    <h5>Details Reporting</h5>
                                    <p className="text-lg neutral-700 w-85 mx-auto">Achieve Business Excellence with
                                        Comprehensive Details Reporting: The Key to Informed Decision-Making and Strategic
                                        Growth</p>
                                </div>
                            </div>
                        </div>
                        <div className="col-lg-4 col-md-6">
                            <div className="card-preparing">
                                <div className="card-image"><img className="wow fadeInUp" src="/assets/imgs/page/homepage1/img-prepare3.png" alt="Nivia" /></div>
                                <div className="card-info">
                                    <h5>Sales Comparison</h5>
                                    <p className="text-lg neutral-700 w-85 mx-auto">Maximizing Your Data Potential: A Deep Dive
                                        into the World of Detailed Reporting for Informed, Strategic Decision-Making</p>
                                </div>
                            </div>
                        </div>
                    </div> */}
        </div>
      </motion.section>
    </>
  );
}
