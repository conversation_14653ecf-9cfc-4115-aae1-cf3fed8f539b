"use client";
import Link from "next/link";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";

export default function Section5() {
  // Use intersection observer to trigger animations when the section is in view
  const [sectionRef, sectionInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [textRef, textInView] = useInView({
    triggerOnce: true,
    threshold: 0.2,
  });

  const [imageRef, imageInView] = useInView({
    triggerOnce: true,
    threshold: 0.3,
  });

  // Animation variants for different elements
  const sectionAnimation = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  const titleAnimation = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const textAnimation = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        delay: 0.2,
        ease: "easeOut",
      },
    },
  };

  const imageAnimation = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <>
      <motion.section
        className="section-box mt-20 mb-20"
        ref={sectionRef}
        initial="hidden"
        animate={sectionInView ? "visible" : "hidden"}
        variants={sectionAnimation}
      >
        <div className="container">
          <div className="block-more-question">
            <motion.div
              className="question-left"
              ref={textRef}
              initial="hidden"
              animate={textInView ? "visible" : "hidden"}
            >
              <motion.h3 className="mb-10" variants={titleAnimation}>
                Everything Connected. Everything Automated.
              </motion.h3>
              <motion.p className="text-lg mb-20" variants={textAnimation}>
                At OMX, our SaaS tools + marketing services are built to work
                together—so your business runs like a smart, synced machine. No
                more juggling platforms. No more wasted efforts. Just real-time
                data, seamless workflows, and higher ROI.
              </motion.p>
              {/* Button is commented out in the original code */}
              {/* <Link className="btn btn-brand-4" href="#">
                Go to Support Center
                <svg
                  width={23}
                  height={22}
                  viewBox="0 0 23 22"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clipPath="url(#clip0_449_3780)">
                    <path
                      d="M22.5 10.9993L18.9791 7.47852V10.3064H0.5V11.6924H18.9791V14.5203L22.5 10.9993Z"
                      fill="#191919"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_449_3780">
                      <rect
                        width={22}
                        height={22}
                        fill="white"
                        transform="translate(0.5)"
                      />
                    </clipPath>
                  </defs>
                </svg>
              </Link> */}
            </motion.div>
            <motion.div
              className="question-right"
              ref={imageRef}
              initial="hidden"
              animate={imageInView ? "visible" : "hidden"}
              variants={imageAnimation}
            >
              <img
                src="/assets/imgs/page/homepage4/bg-question.png"
                alt="Nivia"
              />
            </motion.div>
          </div>
        </div>
      </motion.section>
    </>
  );
}
