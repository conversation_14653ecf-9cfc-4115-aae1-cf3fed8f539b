
import Link from 'next/link'

export default function Section6() {
    return (
        <>

            <section className="section-box wow animate__animated animate__fadeIn box-faqs-2">
                <div className="box-faqs-2-inner">
                    <div className="container">
                        <div className="text-center"><Link className="btn btn-brand-5" href="#">Amazing features</Link>
                            <h2 className="mb-25 mt-15 neutral-0">Frequently Asked Questions</h2>
                            <p className="text-md neutral-500 mb-60">Find answers to common questions in our comprehensive FAQs
                                section,<br className="d-none d-lg-block" />providing clarity and support for your crypto journey
                            </p>
                        </div>
                        <div className="box-faqs-list">
                            <div className="item-faqs">
                                <div className="card-faq">
                                    <div className="item-title">
                                        <h6>
                                            <svg xmlns="http://www.w3.org/2000/svg" width={18} height={18} viewBox="0 0 18 18" fill="none">
                                                <g clipPath="url(#clip0_51_1478)">
                                                    <path d="M9 0C4.02571 0 0 4.02525 0 9C0 13.9742 4.02525 18 9 18C13.9743 18 18 13.9747 18 9C18 4.02571 13.9747 0 9 0ZM9 16.7442C4.72985 16.7442 1.25582 13.2702 1.25582 9C1.25582 4.72982 4.72985 1.25582 9 1.25582C13.2702 1.25582 16.7442 4.72982 16.7442 9C16.7442 13.2702 13.2702 16.7442 9 16.7442Z" fill="true" />
                                                    <path d="M8.73434 11.3887C8.23677 11.3887 7.83398 11.8033 7.83398 12.3008C7.83398 12.7866 8.22492 13.2131 8.73434 13.2131C9.24375 13.2131 9.6465 12.7866 9.6465 12.3008C9.6465 11.8033 9.23187 11.3887 8.73434 11.3887Z" fill="true" />
                                                    <path d="M8.8885 4.48047C7.28917 4.48047 6.55469 5.42825 6.55469 6.06795C6.55469 6.52997 6.94563 6.74323 7.26548 6.74323C7.90521 6.74323 7.6446 5.831 8.85296 5.831C9.44527 5.831 9.91918 6.09164 9.91918 6.6366C9.91918 7.2763 9.25574 7.64355 8.8648 7.97525C8.52122 8.27137 8.07108 8.75712 8.07108 9.77595C8.07108 10.392 8.23695 10.5697 8.72263 10.5697C9.3031 10.5697 9.42157 10.3091 9.42157 10.0839C9.42157 9.46791 9.43342 9.11252 10.085 8.6031C10.4049 8.35434 11.4118 7.54873 11.4118 6.43516C11.4118 5.32158 10.4049 4.48047 8.8885 4.48047Z" fill="true" />
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_51_1478">
                                                        <rect width={18} height={18} fill="white" />
                                                    </clipPath>
                                                </defs>
                                            </svg>How do I purchase an item?
                                        </h6>
                                    </div>
                                    <div className="item-info">
                                        <p className="text-md">If you’re after only one item, simply choose the ‘Buy Now’ option
                                            on the item page. This will take you directly to Checkout. Once the purchase is
                                            successfully processed, you will receive a confirmation email with the details
                                            of your order.</p>
                                    </div>
                                </div>
                            </div>
                            <div className="item-faqs">
                                <div className="card-faq">
                                    <div className="item-title">
                                        <h6>
                                            <svg xmlns="http://www.w3.org/2000/svg" width={18} height={18} viewBox="0 0 18 18" fill="none">
                                                <g clipPath="url(#clip0_51_1478)">
                                                    <path d="M9 0C4.02571 0 0 4.02525 0 9C0 13.9742 4.02525 18 9 18C13.9743 18 18 13.9747 18 9C18 4.02571 13.9747 0 9 0ZM9 16.7442C4.72985 16.7442 1.25582 13.2702 1.25582 9C1.25582 4.72982 4.72985 1.25582 9 1.25582C13.2702 1.25582 16.7442 4.72982 16.7442 9C16.7442 13.2702 13.2702 16.7442 9 16.7442Z" fill="true" />
                                                    <path d="M8.73434 11.3887C8.23677 11.3887 7.83398 11.8033 7.83398 12.3008C7.83398 12.7866 8.22492 13.2131 8.73434 13.2131C9.24375 13.2131 9.6465 12.7866 9.6465 12.3008C9.6465 11.8033 9.23187 11.3887 8.73434 11.3887Z" fill="true" />
                                                    <path d="M8.8885 4.48047C7.28917 4.48047 6.55469 5.42825 6.55469 6.06795C6.55469 6.52997 6.94563 6.74323 7.26548 6.74323C7.90521 6.74323 7.6446 5.831 8.85296 5.831C9.44527 5.831 9.91918 6.09164 9.91918 6.6366C9.91918 7.2763 9.25574 7.64355 8.8648 7.97525C8.52122 8.27137 8.07108 8.75712 8.07108 9.77595C8.07108 10.392 8.23695 10.5697 8.72263 10.5697C9.3031 10.5697 9.42157 10.3091 9.42157 10.0839C9.42157 9.46791 9.43342 9.11252 10.085 8.6031C10.4049 8.35434 11.4118 7.54873 11.4118 6.43516C11.4118 5.32158 10.4049 4.48047 8.8885 4.48047Z" fill="true" />
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_51_1478">
                                                        <rect width={18} height={18} fill="white" />
                                                    </clipPath>
                                                </defs>
                                            </svg>Can I cancel my subscription?
                                        </h6>
                                    </div>
                                    <div className="item-info">
                                        <p className="text-md">To cancel your subscription, you can usually visit your account
                                            settings or contact our customer support team for assistance. Please note that
                                            cancellation policies, such as refund eligibility or any applicable fees.</p>
                                    </div>
                                </div>
                            </div>
                            <div className="item-faqs">
                                <div className="card-faq">
                                    <div className="item-title">
                                        <h6>
                                            <svg xmlns="http://www.w3.org/2000/svg" width={18} height={18} viewBox="0 0 18 18" fill="none">
                                                <g clipPath="url(#clip0_51_1478)">
                                                    <path d="M9 0C4.02571 0 0 4.02525 0 9C0 13.9742 4.02525 18 9 18C13.9743 18 18 13.9747 18 9C18 4.02571 13.9747 0 9 0ZM9 16.7442C4.72985 16.7442 1.25582 13.2702 1.25582 9C1.25582 4.72982 4.72985 1.25582 9 1.25582C13.2702 1.25582 16.7442 4.72982 16.7442 9C16.7442 13.2702 13.2702 16.7442 9 16.7442Z" fill="true" />
                                                    <path d="M8.73434 11.3887C8.23677 11.3887 7.83398 11.8033 7.83398 12.3008C7.83398 12.7866 8.22492 13.2131 8.73434 13.2131C9.24375 13.2131 9.6465 12.7866 9.6465 12.3008C9.6465 11.8033 9.23187 11.3887 8.73434 11.3887Z" fill="true" />
                                                    <path d="M8.8885 4.48047C7.28917 4.48047 6.55469 5.42825 6.55469 6.06795C6.55469 6.52997 6.94563 6.74323 7.26548 6.74323C7.90521 6.74323 7.6446 5.831 8.85296 5.831C9.44527 5.831 9.91918 6.09164 9.91918 6.6366C9.91918 7.2763 9.25574 7.64355 8.8648 7.97525C8.52122 8.27137 8.07108 8.75712 8.07108 9.77595C8.07108 10.392 8.23695 10.5697 8.72263 10.5697C9.3031 10.5697 9.42157 10.3091 9.42157 10.0839C9.42157 9.46791 9.43342 9.11252 10.085 8.6031C10.4049 8.35434 11.4118 7.54873 11.4118 6.43516C11.4118 5.32158 10.4049 4.48047 8.8885 4.48047Z" fill="true" />
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_51_1478">
                                                        <rect width={18} height={18} fill="white" />
                                                    </clipPath>
                                                </defs>
                                            </svg>What are Favorites?
                                        </h6>
                                    </div>
                                    <div className="item-info">
                                        <p className="text-md">Favorites are a feature that allows you to save and bookmark
                                            specific items or content that you find interesting or want to revisit later. By
                                            marking an item as a favorite, you can easily access it in the future without
                                            having to search for it again.</p>
                                    </div>
                                </div>
                            </div>
                            <div className="item-faqs">
                                <div className="card-faq">
                                    <div className="item-title">
                                        <h6>
                                            <svg xmlns="http://www.w3.org/2000/svg" width={18} height={18} viewBox="0 0 18 18" fill="none">
                                                <g clipPath="url(#clip0_51_1478)">
                                                    <path d="M9 0C4.02571 0 0 4.02525 0 9C0 13.9742 4.02525 18 9 18C13.9743 18 18 13.9747 18 9C18 4.02571 13.9747 0 9 0ZM9 16.7442C4.72985 16.7442 1.25582 13.2702 1.25582 9C1.25582 4.72982 4.72985 1.25582 9 1.25582C13.2702 1.25582 16.7442 4.72982 16.7442 9C16.7442 13.2702 13.2702 16.7442 9 16.7442Z" fill="true" />
                                                    <path d="M8.73434 11.3887C8.23677 11.3887 7.83398 11.8033 7.83398 12.3008C7.83398 12.7866 8.22492 13.2131 8.73434 13.2131C9.24375 13.2131 9.6465 12.7866 9.6465 12.3008C9.6465 11.8033 9.23187 11.3887 8.73434 11.3887Z" fill="true" />
                                                    <path d="M8.8885 4.48047C7.28917 4.48047 6.55469 5.42825 6.55469 6.06795C6.55469 6.52997 6.94563 6.74323 7.26548 6.74323C7.90521 6.74323 7.6446 5.831 8.85296 5.831C9.44527 5.831 9.91918 6.09164 9.91918 6.6366C9.91918 7.2763 9.25574 7.64355 8.8648 7.97525C8.52122 8.27137 8.07108 8.75712 8.07108 9.77595C8.07108 10.392 8.23695 10.5697 8.72263 10.5697C9.3031 10.5697 9.42157 10.3091 9.42157 10.0839C9.42157 9.46791 9.43342 9.11252 10.085 8.6031C10.4049 8.35434 11.4118 7.54873 11.4118 6.43516C11.4118 5.32158 10.4049 4.48047 8.8885 4.48047Z" fill="true" />
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_51_1478">
                                                        <rect width={18} height={18} fill="white" />
                                                    </clipPath>
                                                </defs>
                                            </svg>How can I manage my Account?
                                        </h6>
                                    </div>
                                    <div className="item-info">
                                        <p className="text-md">To manage your account, you can usually access an account
                                            management section on our website or platform. Here, you can update your
                                            personal information, such as name, email address, or password.</p>
                                    </div>
                                </div>
                            </div>
                            <div className="item-faqs">
                                <div className="card-faq">
                                    <div className="item-title">
                                        <h6>
                                            <svg xmlns="http://www.w3.org/2000/svg" width={18} height={18} viewBox="0 0 18 18" fill="none">
                                                <g clipPath="url(#clip0_51_1478)">
                                                    <path d="M9 0C4.02571 0 0 4.02525 0 9C0 13.9742 4.02525 18 9 18C13.9743 18 18 13.9747 18 9C18 4.02571 13.9747 0 9 0ZM9 16.7442C4.72985 16.7442 1.25582 13.2702 1.25582 9C1.25582 4.72982 4.72985 1.25582 9 1.25582C13.2702 1.25582 16.7442 4.72982 16.7442 9C16.7442 13.2702 13.2702 16.7442 9 16.7442Z" fill="true" />
                                                    <path d="M8.73434 11.3887C8.23677 11.3887 7.83398 11.8033 7.83398 12.3008C7.83398 12.7866 8.22492 13.2131 8.73434 13.2131C9.24375 13.2131 9.6465 12.7866 9.6465 12.3008C9.6465 11.8033 9.23187 11.3887 8.73434 11.3887Z" fill="true" />
                                                    <path d="M8.8885 4.48047C7.28917 4.48047 6.55469 5.42825 6.55469 6.06795C6.55469 6.52997 6.94563 6.74323 7.26548 6.74323C7.90521 6.74323 7.6446 5.831 8.85296 5.831C9.44527 5.831 9.91918 6.09164 9.91918 6.6366C9.91918 7.2763 9.25574 7.64355 8.8648 7.97525C8.52122 8.27137 8.07108 8.75712 8.07108 9.77595C8.07108 10.392 8.23695 10.5697 8.72263 10.5697C9.3031 10.5697 9.42157 10.3091 9.42157 10.0839C9.42157 9.46791 9.43342 9.11252 10.085 8.6031C10.4049 8.35434 11.4118 7.54873 11.4118 6.43516C11.4118 5.32158 10.4049 4.48047 8.8885 4.48047Z" fill="true" />
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_51_1478">
                                                        <rect width={18} height={18} fill="white" />
                                                    </clipPath>
                                                </defs>
                                            </svg>Is my credit card information secure?
                                        </h6>
                                    </div>
                                    <div className="item-info">
                                        <p className="text-md">We employ industry-standard security measures to protect your
                                            sensitive data during the payment process. These security measures may include
                                            encryption technologies, secure socket layer (SSL) protocols, and compliance
                                            with Payment Card Industry Data Security Standards (PCI DSS).</p>
                                    </div>
                                </div>
                            </div>
                            <div className="item-faqs">
                                <div className="card-faq">
                                    <div className="item-title">
                                        <h6>
                                            <svg xmlns="http://www.w3.org/2000/svg" width={18} height={18} viewBox="0 0 18 18" fill="none">
                                                <g clipPath="url(#clip0_51_1478)">
                                                    <path d="M9 0C4.02571 0 0 4.02525 0 9C0 13.9742 4.02525 18 9 18C13.9743 18 18 13.9747 18 9C18 4.02571 13.9747 0 9 0ZM9 16.7442C4.72985 16.7442 1.25582 13.2702 1.25582 9C1.25582 4.72982 4.72985 1.25582 9 1.25582C13.2702 1.25582 16.7442 4.72982 16.7442 9C16.7442 13.2702 13.2702 16.7442 9 16.7442Z" fill="true" />
                                                    <path d="M8.73434 11.3887C8.23677 11.3887 7.83398 11.8033 7.83398 12.3008C7.83398 12.7866 8.22492 13.2131 8.73434 13.2131C9.24375 13.2131 9.6465 12.7866 9.6465 12.3008C9.6465 11.8033 9.23187 11.3887 8.73434 11.3887Z" fill="true" />
                                                    <path d="M8.8885 4.48047C7.28917 4.48047 6.55469 5.42825 6.55469 6.06795C6.55469 6.52997 6.94563 6.74323 7.26548 6.74323C7.90521 6.74323 7.6446 5.831 8.85296 5.831C9.44527 5.831 9.91918 6.09164 9.91918 6.6366C9.91918 7.2763 9.25574 7.64355 8.8648 7.97525C8.52122 8.27137 8.07108 8.75712 8.07108 9.77595C8.07108 10.392 8.23695 10.5697 8.72263 10.5697C9.3031 10.5697 9.42157 10.3091 9.42157 10.0839C9.42157 9.46791 9.43342 9.11252 10.085 8.6031C10.4049 8.35434 11.4118 7.54873 11.4118 6.43516C11.4118 5.32158 10.4049 4.48047 8.8885 4.48047Z" fill="true" />
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_51_1478">
                                                        <rect width={18} height={18} fill="white" />
                                                    </clipPath>
                                                </defs>
                                            </svg>How do refunds work?
                                        </h6>
                                    </div>
                                    <div className="item-info">
                                        <p className="text-md">Refund policies may vary depending on the specific terms and
                                            conditions of your purchase. Generally, if you are eligible for a refund, you
                                            can request it by contacting our customer support team or following the refund
                                            process outlined in our refund policy.</p>
                                    </div>
                                </div>
                            </div>
                            <div className="item-faqs">
                                <div className="card-faq">
                                    <div className="item-title">
                                        <h6>
                                            <svg xmlns="http://www.w3.org/2000/svg" width={18} height={18} viewBox="0 0 18 18" fill="none">
                                                <g clipPath="url(#clip0_51_1478)">
                                                    <path d="M9 0C4.02571 0 0 4.02525 0 9C0 13.9742 4.02525 18 9 18C13.9743 18 18 13.9747 18 9C18 4.02571 13.9747 0 9 0ZM9 16.7442C4.72985 16.7442 1.25582 13.2702 1.25582 9C1.25582 4.72982 4.72985 1.25582 9 1.25582C13.2702 1.25582 16.7442 4.72982 16.7442 9C16.7442 13.2702 13.2702 16.7442 9 16.7442Z" fill="true" />
                                                    <path d="M8.73434 11.3887C8.23677 11.3887 7.83398 11.8033 7.83398 12.3008C7.83398 12.7866 8.22492 13.2131 8.73434 13.2131C9.24375 13.2131 9.6465 12.7866 9.6465 12.3008C9.6465 11.8033 9.23187 11.3887 8.73434 11.3887Z" fill="true" />
                                                    <path d="M8.8885 4.48047C7.28917 4.48047 6.55469 5.42825 6.55469 6.06795C6.55469 6.52997 6.94563 6.74323 7.26548 6.74323C7.90521 6.74323 7.6446 5.831 8.85296 5.831C9.44527 5.831 9.91918 6.09164 9.91918 6.6366C9.91918 7.2763 9.25574 7.64355 8.8648 7.97525C8.52122 8.27137 8.07108 8.75712 8.07108 9.77595C8.07108 10.392 8.23695 10.5697 8.72263 10.5697C9.3031 10.5697 9.42157 10.3091 9.42157 10.0839C9.42157 9.46791 9.43342 9.11252 10.085 8.6031C10.4049 8.35434 11.4118 7.54873 11.4118 6.43516C11.4118 5.32158 10.4049 4.48047 8.8885 4.48047Z" fill="true" />
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_51_1478">
                                                        <rect width={18} height={18} fill="white" />
                                                    </clipPath>
                                                </defs>
                                            </svg>Can I Get A Refund?
                                        </h6>
                                    </div>
                                    <div className="item-info">
                                        <p className="text-md">Refunds are typically issued based on certain criteria, such as
                                            the type of product or service, the reason for the refund request, and any
                                            applicable refund deadlines.</p>
                                    </div>
                                </div>
                            </div>
                            <div className="item-faqs">
                                <div className="card-faq">
                                    <div className="item-title">
                                        <h6>
                                            <svg xmlns="http://www.w3.org/2000/svg" width={18} height={18} viewBox="0 0 18 18" fill="none">
                                                <g clipPath="url(#clip0_51_1478)">
                                                    <path d="M9 0C4.02571 0 0 4.02525 0 9C0 13.9742 4.02525 18 9 18C13.9743 18 18 13.9747 18 9C18 4.02571 13.9747 0 9 0ZM9 16.7442C4.72985 16.7442 1.25582 13.2702 1.25582 9C1.25582 4.72982 4.72985 1.25582 9 1.25582C13.2702 1.25582 16.7442 4.72982 16.7442 9C16.7442 13.2702 13.2702 16.7442 9 16.7442Z" fill="true" />
                                                    <path d="M8.73434 11.3887C8.23677 11.3887 7.83398 11.8033 7.83398 12.3008C7.83398 12.7866 8.22492 13.2131 8.73434 13.2131C9.24375 13.2131 9.6465 12.7866 9.6465 12.3008C9.6465 11.8033 9.23187 11.3887 8.73434 11.3887Z" fill="true" />
                                                    <path d="M8.8885 4.48047C7.28917 4.48047 6.55469 5.42825 6.55469 6.06795C6.55469 6.52997 6.94563 6.74323 7.26548 6.74323C7.90521 6.74323 7.6446 5.831 8.85296 5.831C9.44527 5.831 9.91918 6.09164 9.91918 6.6366C9.91918 7.2763 9.25574 7.64355 8.8648 7.97525C8.52122 8.27137 8.07108 8.75712 8.07108 9.77595C8.07108 10.392 8.23695 10.5697 8.72263 10.5697C9.3031 10.5697 9.42157 10.3091 9.42157 10.0839C9.42157 9.46791 9.43342 9.11252 10.085 8.6031C10.4049 8.35434 11.4118 7.54873 11.4118 6.43516C11.4118 5.32158 10.4049 4.48047 8.8885 4.48047Z" fill="true" />
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_51_1478">
                                                        <rect width={18} height={18} fill="white" />
                                                    </clipPath>
                                                </defs>
                                            </svg>What is Item Support?
                                        </h6>
                                    </div>
                                    <div className="item-info">
                                        <p className="text-md">Item support refers to the assistance or help provided to
                                            customers after purchasing a product or service. It may include technical
                                            support, troubleshooting guidance, bug fixes, or answering specific questions
                                            related to the purchased item.</p>
                                    </div>
                                </div>
                            </div>
                            <div className="item-faqs">
                                <div className="card-faq">
                                    <div className="item-title">
                                        <h6>
                                            <svg xmlns="http://www.w3.org/2000/svg" width={18} height={18} viewBox="0 0 18 18" fill="none">
                                                <g clipPath="url(#clip0_51_1478)">
                                                    <path d="M9 0C4.02571 0 0 4.02525 0 9C0 13.9742 4.02525 18 9 18C13.9743 18 18 13.9747 18 9C18 4.02571 13.9747 0 9 0ZM9 16.7442C4.72985 16.7442 1.25582 13.2702 1.25582 9C1.25582 4.72982 4.72985 1.25582 9 1.25582C13.2702 1.25582 16.7442 4.72982 16.7442 9C16.7442 13.2702 13.2702 16.7442 9 16.7442Z" fill="true" />
                                                    <path d="M8.73434 11.3887C8.23677 11.3887 7.83398 11.8033 7.83398 12.3008C7.83398 12.7866 8.22492 13.2131 8.73434 13.2131C9.24375 13.2131 9.6465 12.7866 9.6465 12.3008C9.6465 11.8033 9.23187 11.3887 8.73434 11.3887Z" fill="true" />
                                                    <path d="M8.8885 4.48047C7.28917 4.48047 6.55469 5.42825 6.55469 6.06795C6.55469 6.52997 6.94563 6.74323 7.26548 6.74323C7.90521 6.74323 7.6446 5.831 8.85296 5.831C9.44527 5.831 9.91918 6.09164 9.91918 6.6366C9.91918 7.2763 9.25574 7.64355 8.8648 7.97525C8.52122 8.27137 8.07108 8.75712 8.07108 9.77595C8.07108 10.392 8.23695 10.5697 8.72263 10.5697C9.3031 10.5697 9.42157 10.3091 9.42157 10.0839C9.42157 9.46791 9.43342 9.11252 10.085 8.6031C10.4049 8.35434 11.4118 7.54873 11.4118 6.43516C11.4118 5.32158 10.4049 4.48047 8.8885 4.48047Z" fill="true" />
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_51_1478">
                                                        <rect width={18} height={18} fill="white" />
                                                    </clipPath>
                                                </defs>
                                            </svg>Where Is My Purchase Code?
                                        </h6>
                                    </div>
                                    <div className="item-info">
                                        <p className="text-md">The purchase code is typically provided to you upon completing
                                            the purchase of a product or service. It serves as a unique identifier that
                                            enables you to access certain features, updates, or support related to your
                                            purchase.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="text-center">
                            <div className="box-support-faq">Still have questions? Go to our<Link className="brand-4" href="/help"> Support Center</Link> for help</div>
                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}
