
import Link from 'next/link'

export default function Section2() {
    return (
        <>

            <section className="section-box wow animate__animated animate__fadeIn box-our-features-3">
                <div className="container">
                    <div className="block-our-features-3">
                        <div className="lists-our-features mt-35">
                            <div className="item-our-feature feature-big">
                                <div className="card-enjoy-style-2">
                                    <div className="card-image"><Link href="#"><img src="/assets/imgs/page/homepage3/global.png" alt="Nivia" /></Link></div>
                                    <div className="card-info"><Link href="#">
                                        <h5 className="heading-5">Global community</h5>
                                    </Link>
                                        <p className="text-md neutral-600">Become part of a thriving global community,
                                            connecting with like-minded individuals from around the world. Share
                                            experiences, ideas, and insights that transcend borders.</p><Link className="btn btn-link" href="#">Learn More
                                            <svg width={22} height={8} viewBox="0 0 22 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M22 4.00032L18.4791 0.479492V3.3074H0V4.69333H18.4791V7.52129L22 4.00032Z" fill="url(#paint0_linear_197_2071)" />
                                                <defs>
                                                    <linearGradient id="paint0_linear_197_2071" x1="22.7857" y1="1.52272" x2="-0.776803" y2="6.64942" gradientUnits="userSpaceOnUse">
                                                        <stop stopColor="#22D1EE" />
                                                        <stop offset={1} stopColor="#C5FF41" />
                                                    </linearGradient>
                                                </defs>
                                            </svg></Link>
                                    </div>
                                </div>
                            </div>
                            <div className="item-our-feature feature-mid">
                                <div className="card-enjoy-style-2">
                                    <div className="card-image"><Link href="#"><img src="/assets/imgs/page/homepage3/planner.png" alt="Nivia" /></Link></div>
                                    <div className="card-info"><Link href="#">
                                        <h5 className="heading-5">Easy-to-use planner</h5>
                                    </Link>
                                        <p className="text-md neutral-600">Our user-friendly planner is designed to simplify
                                            your life. Whether you're organizing your personal schedule or managing a
                                            complex project</p><Link className="btn btn-link" href="#">Learn More
                                            <svg width={22} height={8} viewBox="0 0 22 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M22 4.00032L18.4791 0.479492V3.3074H0V4.69333H18.4791V7.52129L22 4.00032Z" fill="url(#paint0_linear_197_2071)" />
                                                <defs>
                                                    <linearGradient id="paint0_linear_197_2071" x1="22.7857" y1="1.52272" x2="-0.776803" y2="6.64942" gradientUnits="userSpaceOnUse">
                                                        <stop stopColor="#22D1EE" />
                                                        <stop offset={1} stopColor="#C5FF41" />
                                                    </linearGradient>
                                                </defs>
                                            </svg></Link>
                                    </div>
                                </div>
                            </div>
                            <div className="item-our-feature feature-sm">
                                <div className="card-enjoy-style-2">
                                    <div className="card-image"><Link href="#"><img src="/assets/imgs/page/homepage3/report.png" alt="Nivia" /></Link></div>
                                    <div className="card-info"><Link href="#">
                                        <h5 className="heading-5">Automated reports</h5>
                                    </Link>
                                        <p className="text-md neutral-600">Say goodbye to manual data crunching. Our automated
                                            reporting system streamlines the process of gathering and analyzing data.</p><Link className="btn btn-link" href="#">Learn More
                                            <svg width={22} height={8} viewBox="0 0 22 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M22 4.00032L18.4791 0.479492V3.3074H0V4.69333H18.4791V7.52129L22 4.00032Z" fill="url(#paint0_linear_197_2071)" />
                                                <defs>
                                                    <linearGradient id="paint0_linear_197_2071" x1="22.7857" y1="1.52272" x2="-0.776803" y2="6.64942" gradientUnits="userSpaceOnUse">
                                                        <stop stopColor="#22D1EE" />
                                                        <stop offset={1} stopColor="#C5FF41" />
                                                    </linearGradient>
                                                </defs>
                                            </svg></Link>
                                    </div>
                                </div>
                            </div>
                            <div className="item-our-feature feature-sm">
                                <div className="card-enjoy-style-2">
                                    <div className="card-image"><Link href="#"><img src="/assets/imgs/page/homepage3/trusted.png" alt="Nivia" /></Link></div>
                                    <div className="card-info"><Link href="#">
                                        <h5 className="heading-5">Trusted  Secure</h5>
                                    </Link>
                                        <p className="text-md neutral-600">Trust is at the core of our platform. We prioritize
                                            the security and confidentiality of your information. No more wasted hours on
                                            tedious number-crunching tasks.</p><Link className="btn btn-link" href="#">Learn More
                                            <svg width={22} height={8} viewBox="0 0 22 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M22 4.00032L18.4791 0.479492V3.3074H0V4.69333H18.4791V7.52129L22 4.00032Z" fill="url(#paint0_linear_197_2071)" />
                                                <defs>
                                                    <linearGradient id="paint0_linear_197_2071" x1="22.7857" y1="1.52272" x2="-0.776803" y2="6.64942" gradientUnits="userSpaceOnUse">
                                                        <stop stopColor="#22D1EE" />
                                                        <stop offset={1} stopColor="#C5FF41" />
                                                    </linearGradient>
                                                </defs>
                                            </svg></Link>
                                    </div>
                                </div>
                            </div>
                            <div className="item-our-feature feature-mid">
                                <div className="card-enjoy-style-2">
                                    <div className="card-image"><Link href="#"><img src="/assets/imgs/page/homepage3/payment.png" alt="Nivia" /></Link></div>
                                    <div className="card-info"><Link href="#">
                                        <h5 className="heading-5">Automatic payment</h5>
                                    </Link>
                                        <p className="text-md neutral-600">Seamlessly handle financial transactions through our
                                            automatic payment system. Our system takes care of it all, ensuring your
                                            financial commitments are met promptly and without hassle.</p><Link className="btn btn-link" href="#">Learn More
                                            <svg width={22} height={8} viewBox="0 0 22 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M22 4.00032L18.4791 0.479492V3.3074H0V4.69333H18.4791V7.52129L22 4.00032Z" fill="url(#paint0_linear_197_2071)" />
                                                <defs>
                                                    <linearGradient id="paint0_linear_197_2071" x1="22.7857" y1="1.52272" x2="-0.776803" y2="6.64942" gradientUnits="userSpaceOnUse">
                                                        <stop stopColor="#22D1EE" />
                                                        <stop offset={1} stopColor="#C5FF41" />
                                                    </linearGradient>
                                                </defs>
                                            </svg></Link>
                                    </div>
                                </div>
                            </div>
                            <div className="item-our-feature feature-big">
                                <div className="card-enjoy-style-2">
                                    <div className="card-image"><Link href="#"><img src="/assets/imgs/page/homepage3/support.png" alt="Nivia" /></Link></div>
                                    <div className="card-info"><Link href="#">
                                        <h5 className="heading-5">24/7 Support</h5>
                                    </Link>
                                        <p className="text-md neutral-600">We understand that issues and questions don't always
                                            arise during regular working hours. That's why our 24/7 support team is here for
                                            you, providing assistance at any time of day or night.</p><Link className="btn btn-link" href="#">Learn More
                                            <svg width={22} height={8} viewBox="0 0 22 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M22 4.00032L18.4791 0.479492V3.3074H0V4.69333H18.4791V7.52129L22 4.00032Z" fill="url(#paint0_linear_197_2071)" />
                                                <defs>
                                                    <linearGradient id="paint0_linear_197_2071" x1="22.7857" y1="1.52272" x2="-0.776803" y2="6.64942" gradientUnits="userSpaceOnUse">
                                                        <stop stopColor="#22D1EE" />
                                                        <stop offset={1} stopColor="#C5FF41" />
                                                    </linearGradient>
                                                </defs>
                                            </svg></Link>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}
