export default function Section6() {
  return (
    <>
      <section className="section-box box-our-working">
        <div className="container">
          <div className="text-center">
            <p className="text-22-bold mb-15">How It Work?</p>
            <h2 className="text-48-semibold neutral-1000 mb-35">
              Our Working Process
            </h2>
            <p className="text-xl neutral-700">
              Discover what sets this apart as the market's easiest and most
              powerful video interviewing
              <br className="d-none d-lg-block" /> platform, and why hiring
              managers consistently choose us over the competition
            </p>
          </div>
          <div className="row mt-65">
            <div className="col-lg-3 col-md-6 .col-sm-6">
              <div className="card-working hover-up">
                <div className="card-number">
                  <span>1</span>
                </div>
                <div className="card-info">
                  <h3 className="text-22-bold">Requirement Analysis</h3>
                  <p className="text-md">
                    Pellentesque at posuere tellus. Ut sed dui justo. Phasellus
                    is scelerisque turpis arcu, ut pulvinar lectus tristique
                    non. Nam laoreet, risus vel laoreet laoreet, mauris risus
                    porta velitn platform.
                  </p>
                </div>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 pt-60 mb-30 .col-sm-6">
              <div className="card-working hover-up">
                <div className="card-number">
                  <span>2</span>
                </div>
                <div className="card-info">
                  <h3 className="text-22-bold">Development</h3>
                  <p className="text-md">
                    Pellentesque at posuere tellus. Ut sed dui justo. Phasellus
                    is scelerisque turpis arcu, ut pulvinar lectus tristique
                    non. Nam laoreet, risus vel laoreet laoreet
                  </p>
                </div>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 .col-sm-6">
              <div className="card-working hover-up">
                <div className="card-number">
                  <span>3</span>
                </div>
                <div className="card-info">
                  <h3 className="text-22-bold">
                    Implementation or Test Execution
                  </h3>
                  <p className="text-md">
                    Pellentesque at posuere tellus. Ut sed dui justo. Phasellus
                    is scelerisque turpis arcu, ut pulvinar lectus tristique
                    non. Nam laoreet, risus vel laoreet laoreet, mauris
                  </p>
                </div>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 pt-60 mb-30 .col-sm-6">
              <div className="card-working hover-up">
                <div className="card-number">
                  <span>4</span>
                </div>
                <div className="card-info">
                  <h3 className="text-22-bold">
                    Handover and customer support
                  </h3>
                  <p className="text-md">
                    Pellentesque at posuere tellus. Ut sed dui justo. Phasellus
                    is scelerisque turpis arcu, ut pulvinar lectus tristique
                    non. Nam laoreet, risus vel laoreet laoreet, mauris risus
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
