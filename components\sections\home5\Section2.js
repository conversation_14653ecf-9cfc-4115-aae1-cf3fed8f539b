
import Link from 'next/link'
import Marquee from 'react-fast-marquee'

export default function Section2() {
    return (
        <>

            <section className="section-box wow animate__animated animate__fadeIn box-grow">
                <div className="box-grow-inner">
                    <div className="container">
                        <h2 className="display-2">Grow Your Business Fast<br className="d-none d-lg-block" /> with&nbsp;<span className="text-background"><span className="text-linear-3">Nivia Platform</span></span> and
                            Services</h2>
                    </div>
                    <div className="box-all-group-animate">
                        <div className="box-swiper-group-animate">
                            <div className="carouselTicker7 carouselTicker_vertical" id="slide-grow-1">
                                <Marquee className="carouselTicker__list">
                                    <li className="carouselTicker__item">
                                        <div className="item-grow"><Link href="#"><img alt="Nivia" src="/assets/imgs/page/homepage6/grow.png" /></Link>
                                            <div className="button-view-more"><Link className="btn btn-border-linear" href="#"><span>
                                                <svg width={24} height={24} viewBox="0 0 24 24" fill="true" xmlns="http://www.w3.org/2000/svg">
                                                    <g clipPath="url(#clip0_26_429)">
                                                        <path d="M9.46952 16.8193C9.37887 16.8193 9.28755 16.7881 9.21304 16.7246L6.64906 14.5414C6.56062 14.466 6.50977 14.3559 6.50977 14.2396C6.50977 14.1235 6.56084 14.0134 6.64906 13.938L9.21304 11.7548C9.37953 11.6128 9.62961 11.6329 9.77156 11.7994C9.91329 11.9661 9.89339 12.2162 9.72667 12.3579L7.51714 14.2396L9.72667 16.1212C9.89339 16.2632 9.91329 16.5132 9.77156 16.6797C9.69306 16.7719 9.58163 16.8193 9.46952 16.8193Z" fill="true" />
                                                        <path d="M14.5312 16.8193C14.4191 16.8193 14.3076 16.7719 14.2294 16.6797C14.0874 16.5132 14.1075 16.2632 14.274 16.1212L16.4836 14.2396L14.274 12.3579C14.1075 12.2162 14.0874 11.9661 14.2294 11.7994C14.3711 11.6329 14.6214 11.6128 14.7877 11.7548L17.3516 13.938C17.4401 14.0134 17.4909 14.1235 17.4909 14.2396C17.4909 14.3559 17.4401 14.466 17.3516 14.5414L14.7877 16.7246C14.7132 16.7881 14.6218 16.8193 14.5312 16.8193Z" fill="true" />
                                                        <path d="M11.0922 18.4567C11.065 18.4567 11.0376 18.4538 11.0099 18.4481C10.7959 18.403 10.6588 18.1927 10.7039 17.9786L12.3019 10.3917C12.347 10.1777 12.557 10.0406 12.7713 10.0857C12.9853 10.1308 13.1224 10.3409 13.0773 10.5551L11.4793 18.142C11.44 18.3287 11.2755 18.4567 11.0922 18.4567Z" fill="true" />
                                                        <path d="M21.3396 22.5897H2.66038C1.19354 22.5897 0 21.3964 0 19.9293V4.07542C0 2.60858 1.19354 1.41504 2.66038 1.41504H21.3396C22.8065 1.41504 24 2.60858 24 4.07542V19.9293C24 21.3964 22.8065 22.5897 21.3396 22.5897ZM2.66038 2.20749C1.63045 2.20749 0.792453 3.04549 0.792453 4.07542V19.9293C0.792453 20.9593 1.63045 21.7973 2.66038 21.7973H21.3396C22.3695 21.7973 23.2075 20.9593 23.2075 19.9293V4.07542C23.2075 3.04549 22.3695 2.20749 21.3396 2.20749H2.66038Z" fill="true" />
                                                        <path d="M23.6038 7.76023H0.396226C0.177329 7.76023 0 7.5829 0 7.364C0 7.14532 0.177329 6.96777 0.396226 6.96777H23.6038C23.8227 6.96777 24 7.14532 24 7.364C24 7.5829 23.8227 7.76023 23.6038 7.76023Z" fill="true" />
                                                        <path d="M6.76953 5.86426C6.07835 5.86426 5.51562 5.30154 5.51562 4.61035C5.51562 3.91895 6.07835 3.35645 6.76953 3.35645C7.46094 3.35645 8.02344 3.91895 8.02344 4.61035C8.02344 5.30154 7.46094 5.86426 6.76953 5.86426ZM6.76953 4.1489C6.51526 4.1489 6.30808 4.35586 6.30808 4.61035C6.30808 4.86485 6.51526 5.07181 6.76953 5.07181C7.02403 5.07181 7.23098 4.86485 7.23098 4.61035C7.23098 4.35586 7.02403 4.1489 6.76953 4.1489Z" fill="true" />
                                                        <path d="M2.91797 5.85156C2.22656 5.85156 1.66406 5.28906 1.66406 4.59766C1.66406 3.90625 2.22656 3.34375 2.91797 3.34375C3.60938 3.34375 4.17188 3.90625 4.17188 4.59766C4.17188 5.28906 3.60938 5.85156 2.91797 5.85156ZM2.91797 4.1362C2.66369 4.1362 2.45652 4.34338 2.45652 4.59766C2.45652 4.85215 2.66369 5.05911 2.91797 5.05911C3.17246 5.05911 3.37942 4.85215 3.37942 4.59766C3.37942 4.34338 3.17246 4.1362 2.91797 4.1362Z" fill="true" />
                                                        <path d="M10.6228 5.87576C9.93164 5.87576 9.36914 5.31326 9.36914 4.62185C9.36914 3.93066 9.93164 3.36816 10.6228 3.36816C11.3142 3.36816 11.8767 3.93066 11.8767 4.62185C11.8767 5.31326 11.3142 5.87576 10.6228 5.87576ZM10.6228 4.1604C10.3686 4.1604 10.1616 4.36735 10.1616 4.62185C10.1616 4.87635 10.3686 5.0833 10.6228 5.0833C10.8773 5.0833 11.0843 4.87635 11.0843 4.62185C11.0843 4.36735 10.8773 4.1604 10.6228 4.1604Z" fill="true" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_26_429">
                                                            <rect width={24} height={24} fill="true" />
                                                        </clipPath>
                                                    </defs>
                                                </svg></span> Digital Marketing</Link></div>
                                        </div>
                                    </li>
                                    <li className="carouselTicker__item">
                                        <div className="item-grow"><Link href="#"><img alt="Nivia" src="/assets/imgs/page/homepage6/grow2.png" /></Link>
                                            <div className="button-view-more"><Link className="btn btn-border-linear" href="#"><span>
                                                <svg width={24} height={24} viewBox="0 0 24 24" fill="true" xmlns="http://www.w3.org/2000/svg">
                                                    <g clipPath="url(#clip0_26_374)">
                                                        <path d="M23.7911 17.4621L21.3311 16.2321L19.7655 12.1596C19.7471 12.1115 19.7189 12.0676 19.6828 12.0308C19.6467 11.9941 19.6033 11.9651 19.5555 11.9459L9.84113 8.06273C9.77321 8.03569 9.69887 8.02909 9.62724 8.04375C9.55562 8.0584 9.48985 8.09368 9.43801 8.14523L8.14238 9.44085C8.09152 9.49273 8.05682 9.55828 8.0425 9.62951C8.02819 9.70074 8.03488 9.7746 8.06176 9.8421L11.9449 19.5565C11.9642 19.6043 11.9931 19.6476 12.0299 19.6838C12.0666 19.7199 12.1105 19.748 12.1586 19.7665L16.2311 21.3321L17.4611 23.7921C17.487 23.8463 17.5256 23.8936 17.5736 23.9298C17.6215 23.966 17.6775 23.9902 17.7368 24.0002C17.7567 24.0021 17.7768 24.0021 17.7968 24.0002C17.8961 23.9998 17.9911 23.96 18.0611 23.8896L23.8886 18.0621C23.9315 18.0207 23.9639 17.9697 23.9832 17.9133C24.0024 17.8569 24.0079 17.7967 23.9993 17.7377C23.9892 17.6785 23.965 17.6225 23.9288 17.5745C23.8926 17.5266 23.8454 17.488 23.7911 17.4621ZM8.84738 9.79335L9.05551 9.5871L13.8911 14.4227C13.5724 14.8743 13.4348 15.429 13.5054 15.9772C13.5761 16.5254 13.8498 17.0271 14.2726 17.3831C14.6953 17.7392 15.2363 17.9237 15.7885 17.9001C16.3407 17.8765 16.8639 17.6465 17.2547 17.2557C17.6455 16.8649 17.8755 16.3416 17.8991 15.7894C17.9227 15.2372 17.7382 14.6963 17.3822 14.2735C17.0261 13.8508 16.5244 13.577 15.9762 13.5064C15.4281 13.4358 14.8733 13.5734 14.4218 13.8921L9.58613 9.05648L9.79238 8.84835L19.1243 12.5815L20.5961 16.4102L16.4093 20.5971L12.5805 19.1252L8.84738 9.79335ZM15.693 14.2371C16.03 14.2376 16.3565 14.3549 16.6167 14.5691C16.8769 14.7833 17.0548 15.0811 17.1201 15.4117C17.1854 15.7424 17.134 16.0854 16.9748 16.3824C16.8155 16.6795 16.5582 16.9121 16.2467 17.0407C15.9351 17.1692 15.5886 17.1858 15.2662 17.0876C14.9438 16.9894 14.6655 16.7824 14.4786 16.502C14.2916 16.2215 14.2077 15.8849 14.2411 15.5496C14.2745 15.2142 14.4232 14.9008 14.6618 14.6627C14.9355 14.3898 15.3064 14.2367 15.693 14.2371ZM17.8999 22.9915L16.9624 21.1165L21.1118 16.9671L22.9868 17.9046L17.8999 22.9915Z" fill="true" />
                                                        <path d="M7.17156 20.0625C7.09985 19.9947 7.00492 19.9569 6.90625 19.9569C6.80758 19.9569 6.71265 19.9947 6.64094 20.0625L6.16469 20.5387C4.89377 18.8138 4.19266 16.7354 4.15905 14.5931C4.12544 12.4508 4.76101 10.3514 5.97719 8.58746C6.34156 8.78795 6.76109 8.86497 7.17291 8.80697C7.58473 8.74897 7.96667 8.55907 8.26148 8.26573C8.5563 7.97239 8.74811 7.59142 8.80818 7.17989C8.86826 6.76837 8.79335 6.34846 8.59469 5.98308C10.3586 4.76691 12.458 4.13133 14.6003 4.16494C16.7426 4.19855 18.821 4.89967 20.5459 6.17058L20.0622 6.64121C19.9925 6.71152 19.9534 6.80656 19.9534 6.90558C19.9532 6.95494 19.9626 7.00386 19.9813 7.04955C19.9999 7.09524 20.0274 7.13679 20.0622 7.17183L21.7159 8.81246C21.7862 8.8823 21.8812 8.92151 21.9803 8.92151C22.0794 8.92151 22.1744 8.8823 22.2447 8.81246L23.8891 7.16808C23.9239 7.13326 23.9516 7.0919 23.9705 7.04637C23.9893 7.00085 23.999 6.95205 23.999 6.90277C23.999 6.85349 23.9893 6.80469 23.9705 6.75917C23.9516 6.71364 23.9239 6.67229 23.8891 6.63746L22.2447 4.99308C22.1744 4.92324 22.0794 4.88404 21.9803 4.88404C21.8812 4.88404 21.7862 4.92324 21.7159 4.99308L21.0803 5.62496C19.5138 4.44614 17.6617 3.70552 15.7143 3.47914C13.7668 3.25276 11.7943 3.54879 9.99906 4.33683L12.1609 2.18058C12.4061 2.29771 12.6848 2.32422 12.9476 2.25542C13.2104 2.18661 13.4403 2.02694 13.5966 1.80472C13.7529 1.5825 13.8254 1.31211 13.8013 1.04151C13.7772 0.770911 13.658 0.517598 13.4649 0.326513C13.2718 0.135427 13.0172 0.0189218 12.7464 -0.00233429C12.4756 -0.0235904 12.2059 0.0517768 11.9854 0.210397C11.7648 0.369017 11.6076 0.600635 11.5416 0.86416C11.4755 1.12769 11.505 1.40608 11.6247 1.64996L7.97594 5.30433C7.60547 5.05582 7.16021 4.94365 6.71621 4.98697C6.27222 5.0303 5.85704 5.22643 5.5416 5.54187C5.22616 5.85731 5.03003 6.27249 4.9867 6.71648C4.94338 7.16048 5.05555 7.60574 5.30406 7.97621L1.64969 11.625C1.40486 11.5057 1.12566 11.477 0.861702 11.5441C0.597748 11.6112 0.366135 11.7698 0.20802 11.9915C0.0499042 12.2133 -0.0244728 12.4839 -0.00189499 12.7553C0.0206829 13.0267 0.138753 13.2814 0.331336 13.4739C0.523918 13.6665 0.778539 13.7846 1.04995 13.8072C1.32137 13.8297 1.592 13.7554 1.81376 13.5973C2.03551 13.4391 2.19403 13.2075 2.26114 12.9436C2.32825 12.6796 2.29962 12.4004 2.18031 12.1556L4.34219 9.99933C3.55336 11.794 3.2564 13.7663 3.48178 15.7138C3.70717 17.6612 4.44678 19.5135 5.62469 21.0806L4.99094 21.7162C4.92109 21.7865 4.88189 21.8815 4.88189 21.9806C4.88189 22.0797 4.92109 22.1747 4.99094 22.245L6.63531 23.8893C6.70532 23.9598 6.80039 23.9995 6.89969 24C6.94917 24 6.99817 23.9902 7.04387 23.9713C7.08957 23.9523 7.13105 23.9244 7.16594 23.8893L8.81219 22.245C8.88203 22.1747 8.92124 22.0797 8.92124 21.9806C8.92124 21.8815 8.88203 21.7865 8.81219 21.7162L7.17156 20.0625ZM21.9841 5.78433L23.0978 6.89808L21.9841 8.01183L20.8703 6.89808L21.9841 5.78433ZM12.3747 0.868085C12.4304 0.811906 12.5016 0.773546 12.5791 0.757873C12.6567 0.742199 12.7372 0.749918 12.8103 0.78005C12.8835 0.810182 12.946 0.861369 12.9901 0.927116C13.0341 0.992863 13.0576 1.07021 13.0576 1.14933C13.0576 1.22846 13.0341 1.30581 12.9901 1.37155C12.946 1.4373 12.8835 1.48849 12.8103 1.51862C12.7372 1.54875 12.6567 1.55647 12.5791 1.5408C12.5016 1.52512 12.4304 1.48676 12.3747 1.43058C12.3377 1.39367 12.3084 1.34982 12.2884 1.30157C12.2684 1.25331 12.2581 1.20158 12.2581 1.14933C12.2581 1.09709 12.2684 1.04536 12.2884 0.997103C12.3084 0.948844 12.3377 0.905002 12.3747 0.868085ZM6.07469 6.07496C6.18358 5.96555 6.31301 5.87872 6.45556 5.81948C6.5981 5.76023 6.75095 5.72974 6.90531 5.72974C7.05968 5.72974 7.21252 5.76023 7.35507 5.81948C7.49761 5.87872 7.62705 5.96555 7.73594 6.07496C7.90054 6.2393 8.0127 6.44877 8.05821 6.67687C8.10373 6.90497 8.08055 7.14145 7.99163 7.35637C7.90271 7.5713 7.75202 7.75502 7.55865 7.88428C7.36528 8.01355 7.13791 8.08254 6.90531 8.08254C6.67272 8.08254 6.44535 8.01355 6.25198 7.88428C6.0586 7.75502 5.90792 7.5713 5.819 7.35637C5.73007 7.14145 5.7069 6.90497 5.75242 6.67687C5.79793 6.44877 5.91009 6.2393 6.07469 6.07496ZM1.43219 12.9375C1.35563 13.0013 1.25796 13.0342 1.15838 13.0297C1.0588 13.0252 0.964494 12.9836 0.894006 12.9131C0.823518 12.8427 0.781938 12.7484 0.777439 12.6488C0.772939 12.5492 0.805844 12.4515 0.869689 12.375C0.944727 12.3012 1.04573 12.2599 1.15094 12.2599C1.25615 12.2599 1.35715 12.3012 1.43219 12.375C1.46915 12.4119 1.49848 12.4557 1.51848 12.504C1.53849 12.5522 1.54879 12.604 1.54879 12.6562C1.54879 12.7085 1.53849 12.7602 1.51848 12.8084C1.49848 12.8567 1.46915 12.9005 1.43219 12.9375ZM6.90531 23.0868L5.79156 21.9731L6.90531 20.8593L8.01906 21.9731L6.90531 23.0868Z" fill="true" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_26_374">
                                                            <rect width={24} height={24} fill="true" />
                                                        </clipPath>
                                                    </defs>
                                                </svg></span> Web Design</Link></div>
                                        </div>
                                    </li>
                                    <li className="carouselTicker__item">
                                        <div className="item-grow"><Link href="#"><img alt="Nivia" src="/assets/imgs/page/homepage6/grow.png" /></Link>
                                            <div className="button-view-more"><Link className="btn btn-border-linear" href="#"><span>
                                                <svg width={24} height={24} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M19.9556 8.13979C19.7681 7.88537 19.6149 7.60735 19.5 7.31292C19.4173 6.99269 19.377 6.66301 19.38 6.3323C19.3575 5.7548 19.335 5.15667 18.9862 4.67667C18.6375 4.19667 18.0694 3.9848 17.5238 3.78417C17.209 3.68777 16.9083 3.55034 16.6294 3.37542C16.3816 3.1689 16.1622 2.9306 15.9769 2.66667C15.6337 2.22792 15.2437 1.72917 14.6644 1.54167C14.085 1.35417 13.5394 1.52292 12.9769 1.67855C12.6612 1.78634 12.3328 1.85253 12 1.87542C11.6638 1.85653 11.3316 1.79349 11.0119 1.68792C10.4587 1.5323 9.88687 1.37105 9.33375 1.55105C8.78062 1.73105 8.36625 2.2373 8.02125 2.67605C7.83581 2.93655 7.61708 3.17167 7.37063 3.37542C7.09408 3.54974 6.79593 3.68716 6.48375 3.78417C5.93813 3.9848 5.37563 4.19105 5.02125 4.67667C4.66688 5.1623 4.64625 5.7548 4.6275 6.3323C4.62808 6.66334 4.58522 6.99303 4.5 7.31292C4.38244 7.60644 4.22673 7.8832 4.03688 8.13604C3.73125 8.60479 3.375 9.13167 3.375 9.75042C3.375 10.3692 3.73125 10.896 4.04437 11.361C4.23189 11.6155 4.38508 11.8935 4.5 12.1879C4.58269 12.5081 4.62303 12.8378 4.62 13.1685C4.6425 13.746 4.665 14.3442 5.01375 14.8242C5.3625 15.3042 5.93063 15.516 6.47625 15.7167L6.7425 15.816V22.1254C6.74255 22.1873 6.75792 22.2482 6.78724 22.3027C6.81656 22.3573 6.85891 22.4037 6.91053 22.4378C6.96214 22.472 7.02141 22.4928 7.08304 22.4985C7.14467 22.5042 7.20676 22.4946 7.26375 22.4704L12 20.4773L16.7288 22.4704C16.775 22.4901 16.8247 22.5003 16.875 22.5004C16.9745 22.5004 17.0698 22.4609 17.1402 22.3906C17.2105 22.3203 17.25 22.2249 17.25 22.1254V15.8123L17.5162 15.7129C18.0619 15.5123 18.6244 15.306 18.9788 14.8204C19.3331 14.3348 19.3538 13.7423 19.3725 13.1648C19.3722 12.835 19.4151 12.5066 19.5 12.1879C19.6176 11.8944 19.7733 11.6176 19.9631 11.3648C20.2688 10.896 20.625 10.3692 20.625 9.75042C20.625 9.13167 20.2687 8.60479 19.9556 8.13979ZM12.1462 19.7235C12.1 19.7039 12.0502 19.6938 12 19.6938C11.9498 19.6938 11.9 19.7039 11.8538 19.7235L7.5 21.5629V16.2304C7.68998 16.4163 7.86491 16.6169 8.02313 16.8304C8.36625 17.2692 8.75625 17.7679 9.33563 17.9554C9.50603 18.0092 9.68383 18.0357 9.8625 18.0342C10.2539 18.0128 10.6405 17.9384 11.0119 17.8129C11.3316 17.7073 11.6638 17.6443 12 17.6254C12.3362 17.6443 12.6684 17.7073 12.9881 17.8129C13.5506 17.9685 14.1131 18.1298 14.6756 17.9498C15.2381 17.7698 15.6431 17.2635 15.9881 16.8248C16.143 16.6136 16.3141 16.4149 16.5 16.2304V21.5629L12.1462 19.7235ZM19.3331 10.9429C19.0998 11.2522 18.9105 11.5923 18.7706 11.9535C18.6659 12.3385 18.6141 12.7359 18.6169 13.1348C18.5981 13.6223 18.5794 14.0835 18.3638 14.3798C18.1481 14.676 17.7188 14.8392 17.25 15.0004C16.8716 15.1202 16.5114 15.2911 16.1794 15.5085C15.8742 15.7544 15.6048 16.0416 15.3787 16.3617C15.075 16.7498 14.7863 17.1117 14.4263 17.2354C14.0663 17.3592 13.65 17.2204 13.185 17.0892C12.8015 16.9646 12.4029 16.8927 12 16.8754C11.5943 16.8944 11.193 16.9689 10.8075 17.0967C10.3425 17.2279 9.90375 17.3517 9.56625 17.2429C9.22875 17.1342 8.9175 16.7573 8.61375 16.3673C8.38719 16.0482 8.11779 15.7617 7.81312 15.516C7.48384 15.2966 7.12617 15.1232 6.75 15.0004C6.29063 14.8317 5.85375 14.6723 5.63625 14.3723C5.41875 14.0723 5.40187 13.6223 5.38312 13.1273C5.38586 12.7284 5.33413 12.331 5.22937 11.946C5.08947 11.5848 4.90017 11.2447 4.66687 10.9354C4.3875 10.5304 4.125 10.1404 4.125 9.75042C4.125 9.36042 4.3875 8.97042 4.66687 8.55792C4.90017 8.24865 5.08947 7.90854 5.22937 7.5473C5.33413 7.16237 5.38586 6.76496 5.38312 6.36605C5.40187 5.87855 5.42062 5.4173 5.63625 5.12105C5.85187 4.8248 6.28125 4.66167 6.75 4.50042C7.12838 4.38066 7.48859 4.2097 7.82062 3.9923C8.12576 3.74641 8.39522 3.45929 8.62125 3.13917C8.925 2.75105 9.21375 2.38917 9.57375 2.26542C9.66923 2.23691 9.76848 2.223 9.86812 2.22417C10.1873 2.24642 10.5024 2.30943 10.8056 2.41167C11.1921 2.5371 11.594 2.60902 12 2.62542C12.4057 2.60642 12.807 2.53198 13.1925 2.40417C13.6575 2.27292 14.0963 2.14917 14.4338 2.25792C14.7713 2.36667 15.0825 2.74355 15.3862 3.13355C15.6128 3.45268 15.8822 3.73912 16.1869 3.9848C16.5162 4.2042 16.8738 4.37768 17.25 4.50042C17.7094 4.66917 18.1463 4.82855 18.3638 5.12855C18.5813 5.42855 18.5981 5.87855 18.6169 6.37355C18.6141 6.77246 18.6659 7.16988 18.7706 7.5548C18.9105 7.91604 19.0998 8.25615 19.3331 8.56542C19.6125 8.97042 19.875 9.36042 19.875 9.75042C19.875 10.1404 19.6125 10.5304 19.3331 10.9429Z" fill="true" />
                                                    <path d="M12 5.25C11.11 5.25 10.24 5.51392 9.49994 6.00839C8.75991 6.50285 8.18314 7.20566 7.84254 8.02792C7.50195 8.85019 7.41283 9.75499 7.58647 10.6279C7.7601 11.5008 8.18869 12.3026 8.81802 12.932C9.44736 13.5613 10.2492 13.9899 11.1221 14.1635C11.995 14.3372 12.8998 14.2481 13.7221 13.9075C14.5443 13.5669 15.2471 12.9901 15.7416 12.2501C16.2361 11.51 16.5 10.64 16.5 9.75C16.5 8.55653 16.0259 7.41193 15.182 6.56802C14.3381 5.72411 13.1935 5.25 12 5.25ZM12 13.5C11.2583 13.5 10.5333 13.2801 9.91661 12.868C9.29993 12.456 8.81928 11.8703 8.53545 11.1851C8.25162 10.4998 8.17736 9.74584 8.32206 9.01841C8.46675 8.29098 8.8239 7.6228 9.34835 7.09835C9.8728 6.5739 10.541 6.21675 11.2684 6.07205C11.9958 5.92736 12.7498 6.00162 13.4351 6.28545C14.1203 6.56928 14.706 7.04993 15.118 7.66661C15.5301 8.2833 15.75 9.00832 15.75 9.75C15.75 10.7446 15.3549 11.6984 14.6517 12.4017C13.9484 13.1049 12.9946 13.5 12 13.5Z" fill="true" />
                                                </svg></span> Marketing</Link></div>
                                        </div>
                                    </li>
                                    <li className="carouselTicker__item">
                                        <div className="item-grow"><Link href="#"><img alt="Nivia" src="/assets/imgs/page/homepage6/grow2.png" /></Link>
                                            <div className="button-view-more"><Link className="btn btn-border-linear" href="#"><span>
                                                <svg width={24} height={24} viewBox="0 0 24 24" fill="true" xmlns="http://www.w3.org/2000/svg">
                                                    <g clipPath="url(#clip0_26_429)">
                                                        <path d="M9.46952 16.8193C9.37887 16.8193 9.28755 16.7881 9.21304 16.7246L6.64906 14.5414C6.56062 14.466 6.50977 14.3559 6.50977 14.2396C6.50977 14.1235 6.56084 14.0134 6.64906 13.938L9.21304 11.7548C9.37953 11.6128 9.62961 11.6329 9.77156 11.7994C9.91329 11.9661 9.89339 12.2162 9.72667 12.3579L7.51714 14.2396L9.72667 16.1212C9.89339 16.2632 9.91329 16.5132 9.77156 16.6797C9.69306 16.7719 9.58163 16.8193 9.46952 16.8193Z" fill="true" />
                                                        <path d="M14.5312 16.8193C14.4191 16.8193 14.3076 16.7719 14.2294 16.6797C14.0874 16.5132 14.1075 16.2632 14.274 16.1212L16.4836 14.2396L14.274 12.3579C14.1075 12.2162 14.0874 11.9661 14.2294 11.7994C14.3711 11.6329 14.6214 11.6128 14.7877 11.7548L17.3516 13.938C17.4401 14.0134 17.4909 14.1235 17.4909 14.2396C17.4909 14.3559 17.4401 14.466 17.3516 14.5414L14.7877 16.7246C14.7132 16.7881 14.6218 16.8193 14.5312 16.8193Z" fill="true" />
                                                        <path d="M11.0922 18.4567C11.065 18.4567 11.0376 18.4538 11.0099 18.4481C10.7959 18.403 10.6588 18.1927 10.7039 17.9786L12.3019 10.3917C12.347 10.1777 12.557 10.0406 12.7713 10.0857C12.9853 10.1308 13.1224 10.3409 13.0773 10.5551L11.4793 18.142C11.44 18.3287 11.2755 18.4567 11.0922 18.4567Z" fill="true" />
                                                        <path d="M21.3396 22.5897H2.66038C1.19354 22.5897 0 21.3964 0 19.9293V4.07542C0 2.60858 1.19354 1.41504 2.66038 1.41504H21.3396C22.8065 1.41504 24 2.60858 24 4.07542V19.9293C24 21.3964 22.8065 22.5897 21.3396 22.5897ZM2.66038 2.20749C1.63045 2.20749 0.792453 3.04549 0.792453 4.07542V19.9293C0.792453 20.9593 1.63045 21.7973 2.66038 21.7973H21.3396C22.3695 21.7973 23.2075 20.9593 23.2075 19.9293V4.07542C23.2075 3.04549 22.3695 2.20749 21.3396 2.20749H2.66038Z" fill="true" />
                                                        <path d="M23.6038 7.76023H0.396226C0.177329 7.76023 0 7.5829 0 7.364C0 7.14532 0.177329 6.96777 0.396226 6.96777H23.6038C23.8227 6.96777 24 7.14532 24 7.364C24 7.5829 23.8227 7.76023 23.6038 7.76023Z" fill="true" />
                                                        <path d="M6.76953 5.86426C6.07835 5.86426 5.51562 5.30154 5.51562 4.61035C5.51562 3.91895 6.07835 3.35645 6.76953 3.35645C7.46094 3.35645 8.02344 3.91895 8.02344 4.61035C8.02344 5.30154 7.46094 5.86426 6.76953 5.86426ZM6.76953 4.1489C6.51526 4.1489 6.30808 4.35586 6.30808 4.61035C6.30808 4.86485 6.51526 5.07181 6.76953 5.07181C7.02403 5.07181 7.23098 4.86485 7.23098 4.61035C7.23098 4.35586 7.02403 4.1489 6.76953 4.1489Z" fill="true" />
                                                        <path d="M2.91797 5.85156C2.22656 5.85156 1.66406 5.28906 1.66406 4.59766C1.66406 3.90625 2.22656 3.34375 2.91797 3.34375C3.60938 3.34375 4.17188 3.90625 4.17188 4.59766C4.17188 5.28906 3.60938 5.85156 2.91797 5.85156ZM2.91797 4.1362C2.66369 4.1362 2.45652 4.34338 2.45652 4.59766C2.45652 4.85215 2.66369 5.05911 2.91797 5.05911C3.17246 5.05911 3.37942 4.85215 3.37942 4.59766C3.37942 4.34338 3.17246 4.1362 2.91797 4.1362Z" fill="true" />
                                                        <path d="M10.6228 5.87576C9.93164 5.87576 9.36914 5.31326 9.36914 4.62185C9.36914 3.93066 9.93164 3.36816 10.6228 3.36816C11.3142 3.36816 11.8767 3.93066 11.8767 4.62185C11.8767 5.31326 11.3142 5.87576 10.6228 5.87576ZM10.6228 4.1604C10.3686 4.1604 10.1616 4.36735 10.1616 4.62185C10.1616 4.87635 10.3686 5.0833 10.6228 5.0833C10.8773 5.0833 11.0843 4.87635 11.0843 4.62185C11.0843 4.36735 10.8773 4.1604 10.6228 4.1604Z" fill="true" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_26_429">
                                                            <rect width={24} height={24} fill="true" />
                                                        </clipPath>
                                                    </defs>
                                                </svg></span> Development</Link></div>
                                        </div>
                                    </li>
                                </Marquee>
                            </div>
                            <div className="carouselTicker8 carouselTicker_vertical" id="slide-grow-2">
                                <Marquee direction='right' className="carouselTicker__list">
                                    <li className="carouselTicker__item">
                                        <div className="item-grow"><Link href="#"><img alt="Nivia" src="/assets/imgs/page/homepage6/grow.png" /></Link>
                                            <div className="button-view-more"><Link className="btn btn-border-linear" href="#"><span>
                                                <svg width={24} height={24} viewBox="0 0 24 24" fill="true" xmlns="http://www.w3.org/2000/svg">
                                                    <g clipPath="url(#clip0_26_429)">
                                                        <path d="M9.46952 16.8193C9.37887 16.8193 9.28755 16.7881 9.21304 16.7246L6.64906 14.5414C6.56062 14.466 6.50977 14.3559 6.50977 14.2396C6.50977 14.1235 6.56084 14.0134 6.64906 13.938L9.21304 11.7548C9.37953 11.6128 9.62961 11.6329 9.77156 11.7994C9.91329 11.9661 9.89339 12.2162 9.72667 12.3579L7.51714 14.2396L9.72667 16.1212C9.89339 16.2632 9.91329 16.5132 9.77156 16.6797C9.69306 16.7719 9.58163 16.8193 9.46952 16.8193Z" fill="true" />
                                                        <path d="M14.5312 16.8193C14.4191 16.8193 14.3076 16.7719 14.2294 16.6797C14.0874 16.5132 14.1075 16.2632 14.274 16.1212L16.4836 14.2396L14.274 12.3579C14.1075 12.2162 14.0874 11.9661 14.2294 11.7994C14.3711 11.6329 14.6214 11.6128 14.7877 11.7548L17.3516 13.938C17.4401 14.0134 17.4909 14.1235 17.4909 14.2396C17.4909 14.3559 17.4401 14.466 17.3516 14.5414L14.7877 16.7246C14.7132 16.7881 14.6218 16.8193 14.5312 16.8193Z" fill="true" />
                                                        <path d="M11.0922 18.4567C11.065 18.4567 11.0376 18.4538 11.0099 18.4481C10.7959 18.403 10.6588 18.1927 10.7039 17.9786L12.3019 10.3917C12.347 10.1777 12.557 10.0406 12.7713 10.0857C12.9853 10.1308 13.1224 10.3409 13.0773 10.5551L11.4793 18.142C11.44 18.3287 11.2755 18.4567 11.0922 18.4567Z" fill="true" />
                                                        <path d="M21.3396 22.5897H2.66038C1.19354 22.5897 0 21.3964 0 19.9293V4.07542C0 2.60858 1.19354 1.41504 2.66038 1.41504H21.3396C22.8065 1.41504 24 2.60858 24 4.07542V19.9293C24 21.3964 22.8065 22.5897 21.3396 22.5897ZM2.66038 2.20749C1.63045 2.20749 0.792453 3.04549 0.792453 4.07542V19.9293C0.792453 20.9593 1.63045 21.7973 2.66038 21.7973H21.3396C22.3695 21.7973 23.2075 20.9593 23.2075 19.9293V4.07542C23.2075 3.04549 22.3695 2.20749 21.3396 2.20749H2.66038Z" fill="true" />
                                                        <path d="M23.6038 7.76023H0.396226C0.177329 7.76023 0 7.5829 0 7.364C0 7.14532 0.177329 6.96777 0.396226 6.96777H23.6038C23.8227 6.96777 24 7.14532 24 7.364C24 7.5829 23.8227 7.76023 23.6038 7.76023Z" fill="true" />
                                                        <path d="M6.76953 5.86426C6.07835 5.86426 5.51562 5.30154 5.51562 4.61035C5.51562 3.91895 6.07835 3.35645 6.76953 3.35645C7.46094 3.35645 8.02344 3.91895 8.02344 4.61035C8.02344 5.30154 7.46094 5.86426 6.76953 5.86426ZM6.76953 4.1489C6.51526 4.1489 6.30808 4.35586 6.30808 4.61035C6.30808 4.86485 6.51526 5.07181 6.76953 5.07181C7.02403 5.07181 7.23098 4.86485 7.23098 4.61035C7.23098 4.35586 7.02403 4.1489 6.76953 4.1489Z" fill="true" />
                                                        <path d="M2.91797 5.85156C2.22656 5.85156 1.66406 5.28906 1.66406 4.59766C1.66406 3.90625 2.22656 3.34375 2.91797 3.34375C3.60938 3.34375 4.17188 3.90625 4.17188 4.59766C4.17188 5.28906 3.60938 5.85156 2.91797 5.85156ZM2.91797 4.1362C2.66369 4.1362 2.45652 4.34338 2.45652 4.59766C2.45652 4.85215 2.66369 5.05911 2.91797 5.05911C3.17246 5.05911 3.37942 4.85215 3.37942 4.59766C3.37942 4.34338 3.17246 4.1362 2.91797 4.1362Z" fill="true" />
                                                        <path d="M10.6228 5.87576C9.93164 5.87576 9.36914 5.31326 9.36914 4.62185C9.36914 3.93066 9.93164 3.36816 10.6228 3.36816C11.3142 3.36816 11.8767 3.93066 11.8767 4.62185C11.8767 5.31326 11.3142 5.87576 10.6228 5.87576ZM10.6228 4.1604C10.3686 4.1604 10.1616 4.36735 10.1616 4.62185C10.1616 4.87635 10.3686 5.0833 10.6228 5.0833C10.8773 5.0833 11.0843 4.87635 11.0843 4.62185C11.0843 4.36735 10.8773 4.1604 10.6228 4.1604Z" fill="true" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_26_429">
                                                            <rect width={24} height={24} fill="true" />
                                                        </clipPath>
                                                    </defs>
                                                </svg></span> Digital Marketing</Link></div>
                                        </div>
                                    </li>
                                    <li className="carouselTicker__item">
                                        <div className="item-grow"><Link href="#"><img alt="Nivia" src="/assets/imgs/page/homepage6/grow3.png" /></Link>
                                            <div className="button-view-more"><Link className="btn btn-border-linear" href="#"><span>
                                                <svg width={24} height={24} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M21.9375 12.7983L17.0625 9.98579C17.0055 9.95287 16.9408 9.93555 16.875 9.93555C16.8092 9.93555 16.7445 9.95287 16.6875 9.98579L11.8125 12.7983C11.7553 12.8313 11.7078 12.8789 11.6749 12.9362C11.6419 12.9935 11.6247 13.0585 11.625 13.1245V18.7495C11.6251 18.8153 11.6424 18.8799 11.6753 18.9368C11.7082 18.9938 11.7556 19.041 11.8125 19.0739L16.6875 21.8864C16.7445 21.9193 16.8092 21.9367 16.875 21.9367C16.9408 21.9367 17.0055 21.9193 17.0625 21.8864L21.9375 19.0739C21.9945 19.041 22.0418 18.9938 22.0747 18.9368C22.1076 18.8799 22.1249 18.8153 22.125 18.7495V13.1245C22.1253 13.0585 22.1081 12.9935 22.0751 12.9362C22.0422 12.8789 21.9947 12.8313 21.9375 12.7983ZM16.875 10.7358L21.0863 13.1733L16.875 15.5058L12.75 13.1245L16.875 10.7358ZM12.375 13.7733L16.5 16.1527V20.917L12.375 18.5358V13.7733ZM17.25 20.917V16.1583L21.375 13.8745V18.5339L17.25 20.917Z" fill="true" />
                                                    <path d="M10.5 19.1722L7.5 20.8259V16.2603L10.6875 14.3853C10.7353 14.3637 10.7781 14.3323 10.813 14.2931C10.8479 14.2539 10.8742 14.2079 10.8902 14.1579C10.9062 14.1079 10.9115 14.0552 10.9059 14.003C10.9002 13.9508 10.8836 13.9004 10.8573 13.8551C10.8309 13.8097 10.7953 13.7704 10.7528 13.7397C10.7102 13.7089 10.6617 13.6874 10.6104 13.6766C10.559 13.6658 10.506 13.6659 10.4547 13.6768C10.4034 13.6878 10.3549 13.7094 10.3125 13.7403L7.125 15.6153L2.91563 13.2303L7.125 10.8415L10.3125 12.5197C10.4002 12.5632 10.5015 12.5706 10.5947 12.5404C10.6878 12.5102 10.7655 12.4448 10.8111 12.3581C10.8566 12.2714 10.8664 12.1703 10.8384 12.0765C10.8105 11.9827 10.7469 11.9035 10.6613 11.8559L7.28625 10.0822C7.23106 10.052 7.16916 10.0361 7.10625 10.0361C7.04334 10.0361 6.98144 10.052 6.92625 10.0822L2.0625 12.8497C2.00528 12.8827 1.9578 12.9303 1.92487 12.9876C1.89194 13.0449 1.87474 13.1098 1.875 13.1759V18.6997C1.87474 18.7657 1.89194 18.8307 1.92487 18.888C1.9578 18.9453 2.00528 18.9929 2.0625 19.0259L6.9375 21.7878C6.99451 21.8207 7.05918 21.838 7.125 21.838C7.19083 21.838 7.2555 21.8207 7.3125 21.7878L10.875 19.8265C10.9214 19.8048 10.9628 19.7737 10.9967 19.7353C11.0307 19.6969 11.0564 19.652 11.0723 19.6033C11.0881 19.5546 11.0939 19.5031 11.0891 19.4521C11.0843 19.4011 11.0692 19.3516 11.0445 19.3067C11.0199 19.2618 10.9863 19.2224 10.9458 19.1909C10.9054 19.1595 10.8589 19.1367 10.8093 19.1239C10.7597 19.1111 10.7079 19.1086 10.6573 19.1166C10.6067 19.1246 10.5583 19.1429 10.515 19.1703L10.5 19.1722ZM2.625 13.9222L6.75 16.264V20.8128L2.625 18.4803V13.9222Z" fill="true" />
                                                    <path d="M7.125 9.18711C7.22446 9.18711 7.31984 9.14761 7.39017 9.07728C7.46049 9.00695 7.5 8.91157 7.5 8.81211V5.33587L11.625 7.71524V11.4371C11.625 11.5366 11.6645 11.632 11.7348 11.7023C11.8052 11.7726 11.9005 11.8121 12 11.8121C12.0995 11.8121 12.1948 11.7726 12.2652 11.7023C12.3355 11.632 12.375 11.5366 12.375 11.4371V7.71524L16.5 5.33587V8.81211C16.5 8.91157 16.5395 9.00695 16.6098 9.07728C16.6802 9.14761 16.7755 9.18711 16.875 9.18711C16.9745 9.18711 17.0698 9.14761 17.1402 9.07728C17.2105 9.00695 17.25 8.91157 17.25 8.81211V4.68712C17.2499 4.62135 17.2326 4.55677 17.1997 4.49983C17.1668 4.4429 17.1195 4.39562 17.0625 4.36274L12.1875 1.55024C12.1305 1.51733 12.0658 1.5 12 1.5C11.9342 1.5 11.8695 1.51733 11.8125 1.55024L6.9375 4.36274C6.88055 4.39562 6.83325 4.4429 6.80034 4.49983C6.76743 4.55677 6.75007 4.62135 6.75 4.68712V8.81211C6.75 8.91157 6.78951 9.00695 6.85983 9.07728C6.93016 9.14761 7.02554 9.18711 7.125 9.18711ZM12 2.30774L16.125 4.68712L12 7.06649L7.875 4.68712L12 2.30774Z" fill="true" />
                                                </svg></span> Animation</Link></div>
                                        </div>
                                    </li>
                                    <li className="carouselTicker__item">
                                        <div className="item-grow"><Link href="#"><img alt="Nivia" src="/assets/imgs/page/homepage6/grow4.png" /></Link>
                                            <div className="button-view-more"><Link className="btn btn-border-linear" href="#"><span>
                                                <svg width={24} height={24} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M20.8125 7.46087H20.25V4.25462C20.2501 3.9721 20.1696 3.6954 20.0179 3.45709C19.8662 3.21877 19.6495 3.02875 19.3935 2.90939C19.1374 2.79002 18.8526 2.74628 18.5725 2.7833C18.2924 2.82032 18.0287 2.93657 17.8125 3.11837C16.1705 4.4942 14.0971 5.24884 11.955 5.25024H6.74996C5.52011 5.2473 4.3346 5.7093 3.43105 6.54364C2.5275 7.37798 1.97268 8.52299 1.87782 9.74918C1.78295 10.9754 2.15503 12.1921 2.9195 13.1555C3.68396 14.1189 4.78429 14.7577 5.99996 14.944C6.04871 17.6234 6.70683 20.314 7.1062 21.709C7.17395 21.9381 7.3142 22.1391 7.50589 22.2818C7.69758 22.4245 7.93038 22.5011 8.16933 22.5002C8.27871 22.5009 8.38752 22.4844 8.49183 22.4515L10.2975 21.9021C10.5624 21.8218 10.8074 21.6866 11.0166 21.5053C11.2258 21.324 11.3945 21.1007 11.5116 20.8498C11.6287 20.599 11.6916 20.3263 11.6963 20.0495C11.701 19.7727 11.6473 19.498 11.5387 19.2434L11.325 18.7502C11.0156 18.0396 10.7793 17.4921 10.71 17.0965L11.2293 16.8359C11.3075 16.7957 11.369 16.7295 11.4033 16.6486C11.4375 16.5677 11.4423 16.4774 11.4168 16.3934L11.0043 15.0002H11.9418C14.0724 15.0017 16.1354 15.7486 17.7731 17.1115L17.8162 17.1471C18.0327 17.3291 18.2967 17.4454 18.5772 17.4823C18.8576 17.5192 19.1427 17.4751 19.3989 17.3553C19.6551 17.2355 19.8717 17.0449 20.0232 16.806C20.1746 16.5672 20.2546 16.29 20.2537 16.0071V12.7109H20.8162C21.2638 12.7109 21.693 12.5331 22.0094 12.2166C22.3259 11.9001 22.5037 11.4709 22.5037 11.0234V9.14837C22.5037 8.92645 22.4599 8.7067 22.3749 8.50172C22.2899 8.29674 22.1652 8.11055 22.0081 7.9538C21.851 7.79705 21.6646 7.67283 21.4594 7.58824C21.2542 7.50366 21.0344 7.46038 20.8125 7.46087ZM2.62495 10.1252C2.62495 9.03123 3.05955 7.98202 3.83314 7.20843C4.60673 6.43484 5.65594 6.00024 6.74996 6.00024H7.49996V14.239C7.49996 14.239 7.49996 14.239 7.49996 14.2502H6.74996C5.65594 14.2502 4.60673 13.8156 3.83314 13.0421C3.05955 12.2685 2.62495 11.2193 2.62495 10.1252ZM10.6125 16.3127L10.1456 16.5471C10.0842 16.5776 10.0324 16.6243 9.9957 16.6821C9.95901 16.74 9.93887 16.8068 9.93746 16.8752C9.93746 17.4377 10.215 18.0752 10.635 19.0484C10.7043 19.204 10.7756 19.3709 10.8487 19.5434C10.9173 19.7008 10.9514 19.8711 10.9488 20.0428C10.9462 20.2145 10.907 20.3837 10.8337 20.539C10.7611 20.6942 10.6569 20.8326 10.5277 20.9453C10.3986 21.058 10.2474 21.1425 10.0837 21.1934L8.27433 21.7502C8.22942 21.7639 8.18224 21.7684 8.13556 21.7636C8.08887 21.7588 8.04362 21.7447 8.00245 21.7221C7.96155 21.6999 7.92554 21.6697 7.89654 21.6333C7.86755 21.5969 7.84618 21.5551 7.8337 21.5102C7.52245 20.4152 6.81933 17.6777 6.74996 15.0002H10.2206L10.6125 16.3127ZM19.5 16.0071C19.5 16.1466 19.4603 16.2833 19.3854 16.401C19.3105 16.5187 19.2036 16.6126 19.0772 16.6717C18.9508 16.7307 18.8102 16.7525 18.6718 16.7344C18.5335 16.7164 18.4032 16.6592 18.2962 16.5696L18.2531 16.534C16.4804 15.0594 14.2477 14.2515 11.9418 14.2502H8.24996C8.24996 14.2502 8.24996 14.2502 8.24996 14.239V6.00024H11.955C14.2735 5.99873 16.5176 5.18166 18.2943 3.69212C18.4013 3.60198 18.5318 3.54431 18.6704 3.52591C18.809 3.50751 18.9501 3.52916 19.0768 3.58829C19.2035 3.64743 19.3107 3.74158 19.3857 3.85965C19.4607 3.97771 19.5003 4.11476 19.5 4.25462V16.0071ZM21.75 11.0215C21.75 11.2701 21.6512 11.5086 21.4754 11.6844C21.2996 11.8602 21.0611 11.959 20.8125 11.959H20.25V8.20899H20.8125C21.0611 8.20899 21.2996 8.30777 21.4754 8.48358C21.6512 8.6594 21.75 8.89785 21.75 9.14649V11.0215Z" fill="true" />
                                                </svg></span> Social Media</Link></div>
                                        </div>
                                    </li>
                                    <li className="carouselTicker__item">
                                        <div className="item-grow"><Link href="#"><img alt="Nivia" src="/assets/imgs/page/homepage6/grow2.png" /></Link>
                                            <div className="button-view-more"><Link className="btn btn-border-linear" href="#"><span>
                                                <svg width={24} height={24} viewBox="0 0 24 24" fill="true" xmlns="http://www.w3.org/2000/svg">
                                                    <g clipPath="url(#clip0_26_429)">
                                                        <path d="M9.46952 16.8193C9.37887 16.8193 9.28755 16.7881 9.21304 16.7246L6.64906 14.5414C6.56062 14.466 6.50977 14.3559 6.50977 14.2396C6.50977 14.1235 6.56084 14.0134 6.64906 13.938L9.21304 11.7548C9.37953 11.6128 9.62961 11.6329 9.77156 11.7994C9.91329 11.9661 9.89339 12.2162 9.72667 12.3579L7.51714 14.2396L9.72667 16.1212C9.89339 16.2632 9.91329 16.5132 9.77156 16.6797C9.69306 16.7719 9.58163 16.8193 9.46952 16.8193Z" fill="true" />
                                                        <path d="M14.5312 16.8193C14.4191 16.8193 14.3076 16.7719 14.2294 16.6797C14.0874 16.5132 14.1075 16.2632 14.274 16.1212L16.4836 14.2396L14.274 12.3579C14.1075 12.2162 14.0874 11.9661 14.2294 11.7994C14.3711 11.6329 14.6214 11.6128 14.7877 11.7548L17.3516 13.938C17.4401 14.0134 17.4909 14.1235 17.4909 14.2396C17.4909 14.3559 17.4401 14.466 17.3516 14.5414L14.7877 16.7246C14.7132 16.7881 14.6218 16.8193 14.5312 16.8193Z" fill="true" />
                                                        <path d="M11.0922 18.4567C11.065 18.4567 11.0376 18.4538 11.0099 18.4481C10.7959 18.403 10.6588 18.1927 10.7039 17.9786L12.3019 10.3917C12.347 10.1777 12.557 10.0406 12.7713 10.0857C12.9853 10.1308 13.1224 10.3409 13.0773 10.5551L11.4793 18.142C11.44 18.3287 11.2755 18.4567 11.0922 18.4567Z" fill="true" />
                                                        <path d="M21.3396 22.5897H2.66038C1.19354 22.5897 0 21.3964 0 19.9293V4.07542C0 2.60858 1.19354 1.41504 2.66038 1.41504H21.3396C22.8065 1.41504 24 2.60858 24 4.07542V19.9293C24 21.3964 22.8065 22.5897 21.3396 22.5897ZM2.66038 2.20749C1.63045 2.20749 0.792453 3.04549 0.792453 4.07542V19.9293C0.792453 20.9593 1.63045 21.7973 2.66038 21.7973H21.3396C22.3695 21.7973 23.2075 20.9593 23.2075 19.9293V4.07542C23.2075 3.04549 22.3695 2.20749 21.3396 2.20749H2.66038Z" fill="true" />
                                                        <path d="M23.6038 7.76023H0.396226C0.177329 7.76023 0 7.5829 0 7.364C0 7.14532 0.177329 6.96777 0.396226 6.96777H23.6038C23.8227 6.96777 24 7.14532 24 7.364C24 7.5829 23.8227 7.76023 23.6038 7.76023Z" fill="true" />
                                                        <path d="M6.76953 5.86426C6.07835 5.86426 5.51562 5.30154 5.51562 4.61035C5.51562 3.91895 6.07835 3.35645 6.76953 3.35645C7.46094 3.35645 8.02344 3.91895 8.02344 4.61035C8.02344 5.30154 7.46094 5.86426 6.76953 5.86426ZM6.76953 4.1489C6.51526 4.1489 6.30808 4.35586 6.30808 4.61035C6.30808 4.86485 6.51526 5.07181 6.76953 5.07181C7.02403 5.07181 7.23098 4.86485 7.23098 4.61035C7.23098 4.35586 7.02403 4.1489 6.76953 4.1489Z" fill="true" />
                                                        <path d="M2.91797 5.85156C2.22656 5.85156 1.66406 5.28906 1.66406 4.59766C1.66406 3.90625 2.22656 3.34375 2.91797 3.34375C3.60938 3.34375 4.17188 3.90625 4.17188 4.59766C4.17188 5.28906 3.60938 5.85156 2.91797 5.85156ZM2.91797 4.1362C2.66369 4.1362 2.45652 4.34338 2.45652 4.59766C2.45652 4.85215 2.66369 5.05911 2.91797 5.05911C3.17246 5.05911 3.37942 4.85215 3.37942 4.59766C3.37942 4.34338 3.17246 4.1362 2.91797 4.1362Z" fill="true" />
                                                        <path d="M10.6228 5.87576C9.93164 5.87576 9.36914 5.31326 9.36914 4.62185C9.36914 3.93066 9.93164 3.36816 10.6228 3.36816C11.3142 3.36816 11.8767 3.93066 11.8767 4.62185C11.8767 5.31326 11.3142 5.87576 10.6228 5.87576ZM10.6228 4.1604C10.3686 4.1604 10.1616 4.36735 10.1616 4.62185C10.1616 4.87635 10.3686 5.0833 10.6228 5.0833C10.8773 5.0833 11.0843 4.87635 11.0843 4.62185C11.0843 4.36735 10.8773 4.1604 10.6228 4.1604Z" fill="true" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_26_429">
                                                            <rect width={24} height={24} fill="true" />
                                                        </clipPath>
                                                    </defs>
                                                </svg></span> Development</Link></div>
                                        </div>
                                    </li>
                                </Marquee>
                            </div>
                        </div>
                    </div>
                    <div className="container">
                        <div className="row">
                            <div className="col-lg-6">
                                <h3 className="heading-3">Take your online presence to the next level with my expert web design
                                    and content creation services.</h3>
                            </div>
                            <div className="col-lg-6">
                                <p className="text-xl">Nunc sollicitudin lacus tortor, sit amet commodo mi volutpat sit amet.
                                    Phasellus mattis pharetra porttitor. Praesent eget nibh massa. Pellentesque pharetra
                                    eget nisi sit amet luctus. Ut orci purus, ullamcorper vel commodo id, dapibus vel metus.
                                    Suspendisse ut</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}
