
import Link from 'next/link'

export default function Section6() {
    return (
        <>

            <section className="section-box box-enjoy">
                <div className="container">
                    <div className="row align-items-end">
                        <div className="col-lg-7 mb-40">
                            <h2 className="heading-1 text-linear-3">Enjoy the benefits of having everything in a single platform
                            </h2>
                        </div>
                        <div className="col-lg-5 mb-40">
                            <div className="box-info-num"><span className="text-linear-3 text-88-semibold">2.5x</span>
                                <p className="text-md neutral-0">Page and data loading speed. Improve user experience and order
                                    conversion rate.</p>
                            </div>
                        </div>
                    </div>
                    <div className="row mt-35">
                        <div className="col-lg-4 col-sm-6">
                            <div className="card-enjoy">
                                <div className="card-image"><Link href="#"><img src="/assets/imgs/page/homepage5/ideas.png" alt="Nivia" /></Link></div>
                                <div className="card-info"><Link href="#">
                                    <h5 className="heading-5">Bring your ideas to life</h5>
                                </Link>
                                    <p className="text-lg">Eu morbi orci ultricies vel congue sagittis lgula urna viverra
                                        vestibulum.</p><Link className="btn btn-learmore" href="#">
                                        <svg width={38} height={38} viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect width={38} height={38} rx={19} fill="url(#paint0_linear_15_1041)" />
                                            <g clipPath="url(#clip0_15_1041)">
                                                <path d="M23.6557 16.8139L14.72 25.7497L13.252 24.2817L22.1866 15.3459H14.3119V13.2695H25.7321V24.6897H23.6557V16.8139Z" fill="true" />
                                            </g>
                                            <defs>
                                                <linearGradient id="paint0_linear_15_1041" x1="39.3571" y1="5.62961" x2="-3.06271" y2="8.58385" gradientUnits="userSpaceOnUse">
                                                    <stop stopColor="#22D1EE" />
                                                    <stop offset={1} stopColor="#C5FF41" />
                                                </linearGradient>
                                                <clipPath id="clip0_15_1041">
                                                    <rect width={13} height={13} fill="white" transform="translate(13 13)">
                                                    </rect>
                                                </clipPath>
                                            </defs>
                                        </svg> Learn More</Link>
                                </div>
                            </div>
                        </div>
                        <div className="col-lg-4 col-sm-6">
                            <div className="card-enjoy">
                                <div className="card-image"><Link href="#"><img src="/assets/imgs/page/homepage5/ideas.png" alt="Nivia" /></Link></div>
                                <div className="card-info"><Link href="#">
                                    <h5 className="heading-5">Bring your ideas to life</h5>
                                </Link>
                                    <p className="text-lg">Eu morbi orci ultricies vel congue sagittis lgula urna viverra
                                        vestibulum.</p><Link className="btn btn-learmore" href="#">
                                        <svg width={38} height={38} viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect width={38} height={38} rx={19} fill="url(#paint0_linear_15_1041)" />
                                            <g clipPath="url(#clip0_15_1041)">
                                                <path d="M23.6557 16.8139L14.72 25.7497L13.252 24.2817L22.1866 15.3459H14.3119V13.2695H25.7321V24.6897H23.6557V16.8139Z" fill="true" />
                                            </g>
                                            <defs>
                                                <linearGradient id="paint0_linear_15_1041" x1="39.3571" y1="5.62961" x2="-3.06271" y2="8.58385" gradientUnits="userSpaceOnUse">
                                                    <stop stopColor="#22D1EE" />
                                                    <stop offset={1} stopColor="#C5FF41" />
                                                </linearGradient>
                                                <clipPath id="clip0_15_1041">
                                                    <rect width={13} height={13} fill="white" transform="translate(13 13)">
                                                    </rect>
                                                </clipPath>
                                            </defs>
                                        </svg> Learn More</Link>
                                </div>
                            </div>
                        </div>
                        <div className="col-lg-4 col-sm-6">
                            <div className="card-enjoy">
                                <div className="card-image"><Link href="#"><img src="/assets/imgs/page/homepage5/ideas.png" alt="Nivia" /></Link></div>
                                <div className="card-info"><Link href="#">
                                    <h5 className="heading-5">Bring your ideas to life</h5>
                                </Link>
                                    <p className="text-lg">Eu morbi orci ultricies vel congue sagittis lgula urna viverra
                                        vestibulum.</p><Link className="btn btn-learmore" href="#">
                                        <svg width={38} height={38} viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect width={38} height={38} rx={19} fill="url(#paint0_linear_15_1041)" />
                                            <g clipPath="url(#clip0_15_1041)">
                                                <path d="M23.6557 16.8139L14.72 25.7497L13.252 24.2817L22.1866 15.3459H14.3119V13.2695H25.7321V24.6897H23.6557V16.8139Z" fill="true" />
                                            </g>
                                            <defs>
                                                <linearGradient id="paint0_linear_15_1041" x1="39.3571" y1="5.62961" x2="-3.06271" y2="8.58385" gradientUnits="userSpaceOnUse">
                                                    <stop stopColor="#22D1EE" />
                                                    <stop offset={1} stopColor="#C5FF41" />
                                                </linearGradient>
                                                <clipPath id="clip0_15_1041">
                                                    <rect width={13} height={13} fill="white" transform="translate(13 13)">
                                                    </rect>
                                                </clipPath>
                                            </defs>
                                        </svg> Learn More</Link>
                                </div>
                            </div>
                        </div>
                        <div className="col-lg-4 col-sm-6">
                            <div className="card-enjoy">
                                <div className="card-image"><Link href="#"><img src="/assets/imgs/page/homepage5/ideas.png" alt="Nivia" /></Link></div>
                                <div className="card-info"><Link href="#">
                                    <h5 className="heading-5">Bring your ideas to life</h5>
                                </Link>
                                    <p className="text-lg">Eu morbi orci ultricies vel congue sagittis lgula urna viverra
                                        vestibulum.</p><Link className="btn btn-learmore" href="#">
                                        <svg width={38} height={38} viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect width={38} height={38} rx={19} fill="url(#paint0_linear_15_1041)" />
                                            <g clipPath="url(#clip0_15_1041)">
                                                <path d="M23.6557 16.8139L14.72 25.7497L13.252 24.2817L22.1866 15.3459H14.3119V13.2695H25.7321V24.6897H23.6557V16.8139Z" fill="true" />
                                            </g>
                                            <defs>
                                                <linearGradient id="paint0_linear_15_1041" x1="39.3571" y1="5.62961" x2="-3.06271" y2="8.58385" gradientUnits="userSpaceOnUse">
                                                    <stop stopColor="#22D1EE" />
                                                    <stop offset={1} stopColor="#C5FF41" />
                                                </linearGradient>
                                                <clipPath id="clip0_15_1041">
                                                    <rect width={13} height={13} fill="white" transform="translate(13 13)">
                                                    </rect>
                                                </clipPath>
                                            </defs>
                                        </svg> Learn More</Link>
                                </div>
                            </div>
                        </div>
                        <div className="col-lg-4 col-sm-6">
                            <div className="card-enjoy">
                                <div className="card-image"><Link href="#"><img src="/assets/imgs/page/homepage5/ideas.png" alt="Nivia" /></Link></div>
                                <div className="card-info"><Link href="#">
                                    <h5 className="heading-5">Bring your ideas to life</h5>
                                </Link>
                                    <p className="text-lg">Eu morbi orci ultricies vel congue sagittis lgula urna viverra
                                        vestibulum.</p><Link className="btn btn-learmore" href="#">
                                        <svg width={38} height={38} viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect width={38} height={38} rx={19} fill="url(#paint0_linear_15_1041)" />
                                            <g clipPath="url(#clip0_15_1041)">
                                                <path d="M23.6557 16.8139L14.72 25.7497L13.252 24.2817L22.1866 15.3459H14.3119V13.2695H25.7321V24.6897H23.6557V16.8139Z" fill="true" />
                                            </g>
                                            <defs>
                                                <linearGradient id="paint0_linear_15_1041" x1="39.3571" y1="5.62961" x2="-3.06271" y2="8.58385" gradientUnits="userSpaceOnUse">
                                                    <stop stopColor="#22D1EE" />
                                                    <stop offset={1} stopColor="#C5FF41" />
                                                </linearGradient>
                                                <clipPath id="clip0_15_1041">
                                                    <rect width={13} height={13} fill="white" transform="translate(13 13)">
                                                    </rect>
                                                </clipPath>
                                            </defs>
                                        </svg> Learn More</Link>
                                </div>
                            </div>
                        </div>
                        <div className="col-lg-4 col-sm-6">
                            <div className="card-enjoy">
                                <div className="card-image"><Link href="#"><img src="/assets/imgs/page/homepage5/ideas.png" alt="Nivia" /></Link></div>
                                <div className="card-info"><Link href="#">
                                    <h5 className="heading-5">Bring your ideas to life</h5>
                                </Link>
                                    <p className="text-lg">Eu morbi orci ultricies vel congue sagittis lgula urna viverra
                                        vestibulum.</p><Link className="btn btn-learmore" href="#">
                                        <svg width={38} height={38} viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect width={38} height={38} rx={19} fill="url(#paint0_linear_15_1041)" />
                                            <g clipPath="url(#clip0_15_1041)">
                                                <path d="M23.6557 16.8139L14.72 25.7497L13.252 24.2817L22.1866 15.3459H14.3119V13.2695H25.7321V24.6897H23.6557V16.8139Z" fill="true" />
                                            </g>
                                            <defs>
                                                <linearGradient id="paint0_linear_15_1041" x1="39.3571" y1="5.62961" x2="-3.06271" y2="8.58385" gradientUnits="userSpaceOnUse">
                                                    <stop stopColor="#22D1EE" />
                                                    <stop offset={1} stopColor="#C5FF41" />
                                                </linearGradient>
                                                <clipPath id="clip0_15_1041">
                                                    <rect width={13} height={13} fill="white" transform="translate(13 13)">
                                                    </rect>
                                                </clipPath>
                                            </defs>
                                        </svg> Learn More</Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}
