"use client";
import Link from "next/link";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";

export default function Ready() {
  // Create ref for section to trigger animations when in view
  const [sectionRef, sectionInView] = useInView({
    triggerOnce: true,
    threshold: 0.2,
  });

  // Animation variants
  const containerAnimation = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const titleAnimation = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.7,
        ease: "easeOut",
      },
    },
  };

  const textAnimation = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const buttonAnimation = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: [0.175, 0.885, 0.32, 1.275], // Custom spring-like easing
      },
    },
  };

  return (
    <>
      <section className="section-box box-newsletter">
        <div className="container">
          <motion.div
            className="block-newsletter"
            ref={sectionRef}
            initial="hidden"
            animate={sectionInView ? "visible" : "hidden"}
            variants={containerAnimation}
          >
            <div className="block-newsletter-inner">
              {/* <p className="text-lg mb-5">$20 discount for your first order</p> */}
              <motion.h3 className="heading-1 mb-55" variants={titleAnimation}>
                Ready to Work Smarter?
              </motion.h3>
              <motion.p className="text-lg mb-35" variants={textAnimation}>
                Let AI do the heavy lifting while you focus on what
                matters—growth, innovation, and impact. OMX is here to build
                smarter businesses together—with AI, automation, and a digital
                presence that drives real results.
              </motion.p>
              {/* <div className="form-newsletter">
                                <form>
                                    <input className="form-control border-gradient border-gradient-green" type="text" placeholder="Email Address..." />
                                    <button className="btn btn-green-linear">Subscribe
                                        <svg width={22} height={22} viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M22 11.0003L18.4791 7.47949V10.3074H0V11.6933H18.4791V14.5213L22 11.0003Z" fill="true" />
                                        </svg>
                                    </button>
                                </form>
                            </div> */}
              <motion.div variants={buttonAnimation}>
                <Link className="btn btn-brand-4" href="/contact">
                  Contact Us
                  <svg
                    width={23}
                    height={8}
                    viewBox="0 0 23 8"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M22.5 3.99934L18.9791 0.478516V3.30642H0.5V4.69236H18.9791V7.52031L22.5 3.99934Z"
                      fill="true"
                    />
                  </svg>
                </Link>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>
    </>
  );
}
