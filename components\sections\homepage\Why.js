"use client";
import Link from "next/link";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";

export default function Why() {
  // Create refs for different elements to trigger animations
  const [titleRef, titleInView] = useInView({
    triggerOnce: true,
    threshold: 0.2,
  });

  const [imageRef, imageInView] = useInView({
    triggerOnce: true,
    threshold: 0.3,
  });

  const [featuresRef, featuresInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Animation variants
  const titleAnimation = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.7,
        ease: "easeOut",
      },
    },
  };

  const imageAnimation = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  const featuresContainerAnimation = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
        duration: 0.5,
      },
    },
  };

  const featureCardAnimation = {
    hidden: { opacity: 0, x: 30 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  return (
    <>
      <section className="section-box box-companion ">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-7 mb-30">
              <motion.h2
                className="heading-1 neutral-0"
                ref={titleRef}
                initial="hidden"
                animate={titleInView ? "visible" : "hidden"}
                variants={titleAnimation}
              >
                Why{" "}
                <span className="text-bg-brand-4 neutral-1000">Businesses</span>{" "}
                Choose OMX
              </motion.h2>
            </div>
            {/* <div className="col-lg-5 mb-30">
                            <p className="text-xl neutral-700">Weekly workout schedules are customized by your Future Coach for
                                you,</p>
                        </div> */}
          </div>
          <div className="row mt-10 ">
            <div className="col-lg-6">
              <motion.img
                alt="Nivia"
                src="/assets/imgs/page/homepage3/img-companion6.png"
                ref={imageRef}
                initial="hidden"
                animate={imageInView ? "visible" : "hidden"}
                variants={imageAnimation}
              />
            </div>
            <div className="col-lg-6">
              {/* <h2 className="heading-2 neutral-0 mb-20">Find actionable insights, and generate reports</h2>
                            <p className="text-lg neutral-200">Nivia is an independent web design studio with a rich history.
                                Founded in 1999, it gathered the best web designers  developers.</p> */}
              <motion.div
                className="box-list-feature mt-55"
                ref={featuresRef}
                initial="hidden"
                animate={featuresInView ? "visible" : "hidden"}
                variants={featuresContainerAnimation}
              >
                <motion.div
                  className="card-feature-2 card-feature-brand-4"
                  variants={featureCardAnimation}
                >
                  <div className="card-image">
                    <img src="/assets/imgs/page/home-page/artificial-intelligence_18830535.gif" />
                  </div>
                  <div className="card-info">
                    <Link href="#">
                      <h3 className="text-22-bold">AI-Powered Efficiency</h3>
                    </Link>
                    <p className="text-md neutral-200">
                      Automate tasks, speed up decisions, and scale faster
                    </p>
                  </div>
                </motion.div>

                <motion.div
                  className="card-feature-2 card-feature-brand-4"
                  variants={featureCardAnimation}
                >
                  <div className="card-image">
                    <img src="/assets/imgs/page/home-page/solar-system_18471606.gif" />
                  </div>
                  <div className="card-info">
                    <Link href="#">
                      <h3 className="text-22-bold">
                        One Ecosystem, Total Control{" "}
                      </h3>
                    </Link>
                    <p className="text-md neutral-200">
                      Marketing, operations, and sales—all in sync
                    </p>
                  </div>
                </motion.div>

                <motion.div
                  className="card-feature-2 card-feature-brand-4"
                  variants={featureCardAnimation}
                >
                  <div className="card-image">
                    <img src="/assets/imgs/page/home-page/inheritance_11259475.gif" />
                  </div>
                  <div className="card-info">
                    <Link href="#">
                      <h3 className="text-22-bold">
                        Conversion-Focused Everything{" "}
                      </h3>
                    </Link>
                    <p className="text-md neutral-200">
                      Tools that don't just work, they sell
                    </p>
                  </div>
                </motion.div>

                <motion.div
                  className="card-feature-2 card-feature-brand-4"
                  variants={featureCardAnimation}
                >
                  <div className="card-image">
                    <img src="/assets/imgs/page/home-page/modelling_15568251.gif" />
                  </div>
                  <div className="card-info">
                    <Link href="#">
                      <h3 className="text-22-bold">Scalable & Future-Ready</h3>
                    </Link>
                    <p className="text-md neutral-200">
                      Whether you're at 10 clients or 10,000—we grow with you
                    </p>
                  </div>
                </motion.div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
