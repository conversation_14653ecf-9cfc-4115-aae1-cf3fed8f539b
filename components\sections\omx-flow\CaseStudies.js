'use client'
import { createContext, useContext, useState } from 'react'
import Link from 'next/link'
import { Autoplay, Navigation, Pagination } from "swiper/modules"
import { Swiper, SwiperSlide } from "swiper/react"

// Create a context for case studies
const CaseStudiesContext = createContext([])

// Case studies data
const caseStudiesData = [
  {
    id: 1,
    image: "/assets/imgs/page/omx-flow/caseimg/Case Study 1 Financial Institutions – AI-Powered Loan & Investment Assistance.png",
    heading: "Financial Institutions – AI-Powered Loan & Investment Assistance",
    paragraph: "Client: A leading Non-Banking Financial Company (NBFC) offering personal loans, home loans, and investment products across Tier 1 & Tier 2 cities",
    link: "/omx-flow/case-studies/case-1"
  },
  {
    id: 2,
    image: "/assets/imgs/page/omx-flow/caseimg/Case Study 2 Educational Institutes – Automated Admission & Student Support.png",
    heading: "Educational Institutes – Automated Admission & Student Support",
    paragraph: "Client: A network of private schools with 8 campuses across two states",
    link: "/omx-flow/case-studies/case-2"
  },
  {
    id: 3,
    image: "/assets/imgs/page/omx-flow/caseimg/Case Study 3 Digital Marketing Agencies – Lead Generation & Client Retention.png",
    heading: "Digital Marketing Agencies – Lead Generation & Client Retention",
    paragraph: "Client: A performance marketing agency managing 40+ clients across industries",
    link: "/omx-flow/case-studies/case-3"
  },
  {
    id: 4,
    image: "/assets/imgs/page/omx-flow/caseimg/Case Study 4 Interior Designers – Streamlining Client Consultation & Project Updates.png",
    heading: "Interior Designers – Streamlining Client Consultation & Project Updates",
    paragraph: "Client: A high-end interior design studio managing 8–10 projects monthly",
    link: "/omx-flow/case-studies/case-4"
  },
  {
    id: 5,
    image: "/assets/imgs/page/omx-flow/caseimg/Case Study 5 Corporate Offices – HR & Employee Engagement Automation.png",
    heading: "Corporate Offices – HR & Employee Engagement Automation",
    paragraph: "Client: A growing IT company with over 500 employees across 3 locations",
    link: "/omx-flow/case-studies/case-5"
  },
  {
    id: 6,
    image: "/assets/imgs/page/omx-flow/caseimg/Case Study 6 Manufacturers – Dealer & B2B Customer Engagement.png",
    heading: "Manufacturers – Dealer & B2B Customer Engagement",
    paragraph: "Client: A machinery manufacturing firm distributing through 200+ dealers",
    link: "/omx-flow/case-studies/case-6"
  },
  {
    id: 7,
    image: "/assets/imgs/page/omx-flow/caseimg/Case Study 7 Traders – Inventory & Customer Query Automation.png",
    heading: "Traders – Inventory & Customer Query Automation",
    paragraph: "Client: A B2B textile trader supplying to boutiques and resellers across India",
    link: "/omx-flow/case-studies/case-7"
  },
  {
    id: 8,
    image: "/assets/imgs/page/omx-flow/caseimg/Case Study 8 Restaurants & Food Chains – Ordering & Customer Engagement.png",
    heading: "Restaurants & Food Chains – Ordering & Customer Engagement",
    paragraph: "Client: A chain of fast-casual restaurants with 10 outlets",
    link: "/omx-flow/case-studies/case-8"
  },
  {
    id: 9,
    image: "/assets/imgs/page/omx-flow/caseimg/Case Study 9 Banking & Finance – AI-Powered Customer Support & Lead Qualification.png",
    heading: "Banking & Finance – AI-Powered Customer Support & Lead Qualification",
    paragraph: "Client: A private bank offering credit cards, loans, and investment services",
    link: "/omx-flow/case-studies/case-9"
  }
];
// Provider component
function CaseStudiesProvider({ children }) {
  const [caseStudies, setCaseStudies] = useState(caseStudiesData)
  
  return (
    <CaseStudiesContext.Provider value={{ caseStudies, setCaseStudies }}>
      {children}
    </CaseStudiesContext.Provider>
  )
}

// Hook to use the case studies context
function useCaseStudies() {
  const context = useContext(CaseStudiesContext)
  if (!context) {
    throw new Error('useCaseStudies must be used within a CaseStudiesProvider')
  }
  return context
}

// Component to generate the Learn More button
function LearnMoreButton({ link }) {
  return (
    <Link className="btn btn-learmore-2" href={link} target='_blank'>
      <span>
        <svg width={13} height={13} viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clipPath="url(#clip0_24_999)">
            <path d="M10.6557 3.81393L1.71996 12.7497L0.251953 11.2817L9.18664 2.34592H1.31195V0.269531H12.7321V11.6897H10.6557V3.81393Z" fill="#191919" />
          </g>
          <defs>
            <clipPath id="clip0_24_999">
              <rect width={13} height={13} fill="white" />
            </clipPath>
          </defs>
        </svg>
      </span> Learn More
    </Link>
  )
}

// Component to generate a single case study card
function CaseStudyCard({ caseStudy }) {
  const { image, heading, paragraph, link } = caseStudy
  
  return (
    <div className="card-news">
      <div className="card-image">
        <Link href={link} target='_blank'>
          <img src={image} alt="Case Study" />
        </Link>
      </div>
      <div className="card-info">
        <Link className="heading-4" href={link} target='_blank'>
          {heading}
        </Link>
        <p className="text-md neutral-700 mt-15 mb-35">
          {paragraph}
        </p>
        <LearnMoreButton link={link} target='_blank'/>
      </div>
    </div>
  )
}

// Swiper options
const swiperOptions = {
  modules: [Autoplay, Pagination, Navigation],
  spaceBetween: 30,
  slidesPerView: 3,
  slidesPerGroup: 1,
  loop: true,
  navigation: {
    nextEl: ".swiper-button-next-3",
    prevEl: ".swiper-button-prev-3"
  },
  autoplay: {
    delay: 10000
  },
  breakpoints: {
    1199: {
      slidesPerView: 3
    },
    800: {
      slidesPerView: 2
    },
    400: {
      slidesPerView: 1
    },
    250: {
      slidesPerView: 1
    }
  }
}

export default function CaseStudies() {
  return (
    <CaseStudiesProvider>
      <CaseStudiesComponent />
    </CaseStudiesProvider>
  )
}

function CaseStudiesComponent() {
  const { caseStudies } = useCaseStudies()
  
  return (
    <section className="section-box box-latest-news box-latest-news-2">
      <div className="container">
        <div className="row align-items-end">
          <div className="col-lg-8 mb-30">
            <h2 className="heading-2 mb-10">Case Studies</h2>
          </div>
          <div className="col-lg-4 mb-30">
            <div className="box-button-slider box-button-slider-team justify-content-end">
              <div className="swiper-button-prev swiper-button-prev-testimonials swiper-button-prev-3">
                <svg width={16} height={16} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M6.66667 3.33398L2 8.00065M2 8.00065L6.66667 12.6673M2 8.00065H14" stroke="true" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round">
                  </path>
                </svg>
              </div>
              <div className="swiper-button-next swiper-button-next-testimonials swiper-button-next-3">
                <svg width={16} height={16} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9.33333 3.33398L14 8.00065M14 8.00065L9.33333 12.6673M14 8.00065H2" stroke="true" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round">
                  </path>
                </svg>
              </div>
            </div>
          </div>
        </div>
        <div className="box-swiper mt-30">
          <div className="swiper-container swiper-group-3">
            <Swiper {...swiperOptions}>
              {caseStudies.map((caseStudy) => (
                <SwiperSlide key={caseStudy.id}>
                  <CaseStudyCard caseStudy={caseStudy} />
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </div>
      </div>
    </section>
  )
}