"use client";
import { motion } from "framer-motion";
import { useRef } from "react";
import { useInView } from "framer-motion";

const AnimatedFeatureBox = ({ children, delay = 0 }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.3 });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.6, delay }}
      className="box-border-rounded"
    >
      {children}
    </motion.div>
  );
};

const AnimatedHeading = ({ children }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.3 });

  return (
    <motion.h3
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5 }}
      className="heading-3 mb-20"
    >
      {children}
    </motion.h3>
  );
};

const AnimatedImage = ({ src, alt, className = "" }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.3 });

  return (
    <motion.img
      ref={ref}
      src={src}
      alt={alt}
      className={className}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}
      transition={{ duration: 0.7 }}
    />
  );
};

const AnimatedFeatureCard = ({ imageUrl, text, index = 0 }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.3 });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, x: -20 }}
      animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
      transition={{ duration: 0.5, delay: 0.1 * index }}
      className="card-feature-2"
    >
      <div className="card-image">
        <img src={imageUrl} />
      </div>
      <div className="card-info">
        <p className="text-md neutral-700">{text}</p>
      </div>
    </motion.div>
  );
};

export default function FeaturesSection() {
  return (
    <section
      className="section-box box-benifit box-our-working"
      style={{
        background: "linear-gradient(to bottom,rgb(193, 243, 209), #ffffff)",
      }}
    >
      <div className="container">
        {/* feature-1 */}
        <AnimatedFeatureBox>
          <div className="row align-items-center">
            <div className="col-lg-6 mb-40 text-center text-lg-start">
              <div className="image-feature-2">
                <AnimatedImage
                  className="rounded"
                  src="/assets/imgs/page/omx-flow/Whatsapp Chatbot-01.png"
                  alt="OMX Digital"
                />
              </div>
            </div>
            <div className="col-lg-6 mb-40">
              <AnimatedHeading>
                AI WhatsApp Chatbots & Multi-Channel Support
              </AnimatedHeading>
              <div className="row mt-50">
                <div className="col-lg-6 col-md-6">
                  <AnimatedFeatureCard
                    imageUrl="/assets/imgs/page/omx-flow/ad.gif"
                    text="Build WhatsApp, Instagram, & Facebook bots via a no-code bot builder."
                    index={0}
                  />
                </div>
                <div className="col-lg-6 col-md-6">
                  <AnimatedFeatureCard
                    imageUrl="/assets/imgs/page/omx-flow/clock.gif"
                    text="24/7 automated AI assistant to engage customers."
                    index={1}
                  />
                </div>
                <div className="col-lg-6 col-md-6">
                  <AnimatedFeatureCard
                    imageUrl="/assets/imgs/page/omx-flow/aibot.gif"
                    text="Custom AI Flows powered by ChatGPT for smarter interactions."
                    index={2}
                  />
                </div>
              </div>
            </div>
          </div>
        </AnimatedFeatureBox>

        {/* feature-2 */}
        <AnimatedFeatureBox delay={0.2}>
          <div className="row align-items-center">
            <div className="col-lg-6 mb-40">
              <AnimatedHeading>AI + Human Hybrid Chat Support</AnimatedHeading>
              <div className="row mt-50">
                <div className="col-lg-6 col-md-6">
                  <AnimatedFeatureCard
                    imageUrl="/assets/imgs/page/omx-flow/relationship.gif"
                    text="Seamless human handover when AI can't resolve queries."
                    index={0}
                  />
                </div>
                <div className="col-lg-6 col-md-6">
                  <AnimatedFeatureCard
                    imageUrl="/assets/imgs/page/omx-flow/email.gif"
                    text="Unified Team Inbox – Manage WhatsApp, Instagram, and Facebook from a single dashboard."
                    index={1}
                  />
                </div>
              </div>
            </div>
            <div className="col-lg-6 mb-40 text-center text-lg-end">
              <div className="image-feature-2">
                <AnimatedImage
                  src="/assets/imgs/page/omx-flow/AI+Human-01.png"
                  alt="OMX Digital"
                />
              </div>
            </div>
          </div>
        </AnimatedFeatureBox>
      </div>

      {/* feature-3 */}
      <div className="container">
        <AnimatedFeatureBox delay={0.3}>
          <div className="row align-items-center">
            <div className="col-lg-6 mb-40 text-center text-lg-start">
              <div className="image-feature-2">
                <AnimatedImage
                  className="rounded-5"
                  src="/assets/imgs/page/omx-flow/Bulk Whatsapp-01.png"
                  alt="OMX Digital"
                />
              </div>
            </div>
            <div className="col-lg-6 mb-40">
              <AnimatedHeading>
                Bulk WhatsApp & Omni-Channel Marketing
              </AnimatedHeading>
              <div className="row mt-50">
                <div className="col-lg-6 col-md-6">
                  <AnimatedFeatureCard
                    imageUrl="/assets/imgs/page/omx-flow/promotion.gif"
                    text="Run high-converting broadcast campaigns for offers & promotions."
                    index={0}
                  />
                </div>
                <div className="col-lg-6 col-md-6">
                  <AnimatedFeatureCard
                    imageUrl="/assets/imgs/page/omx-flow/resend.gif"
                    text="Automate replies to comments, DMs & mentions on Instagram & Facebook."
                    index={1}
                  />
                </div>
              </div>
            </div>
          </div>
        </AnimatedFeatureBox>

        {/* feature-4 */}
        <AnimatedFeatureBox delay={0.4}>
          <div className="row align-items-center">
            <div className="col-lg-6 mb-40">
              <AnimatedHeading>
                WhatsApp Product Catalog & Payment Integration
              </AnimatedHeading>
              <div className="row mt-50">
                <div className="col-lg-6 col-md-6">
                  <AnimatedFeatureCard
                    imageUrl="/assets/imgs/page/omx-flow/boxes.gif"
                    text="Showcase your products & services directly inside WhatsApp."
                    index={0}
                  />
                </div>
                <div className="col-lg-6 col-md-6">
                  <AnimatedFeatureCard
                    imageUrl="/assets/imgs/page/omx-flow/safety.gif"
                    text="Accept payments via UPI, Razorpay, Stripe & other gateways."
                    index={1}
                  />
                </div>
              </div>
            </div>
            <div className="col-lg-6 mb-40 text-center text-lg-end">
              <div className="image-feature-2">
                <AnimatedImage
                  src="/assets/imgs/page/omx-flow/Payment Integration-01.png"
                  alt="OMX Digital"
                />
              </div>
            </div>
          </div>
        </AnimatedFeatureBox>

        {/* feature-5 */}
        <AnimatedFeatureBox delay={0.5}>
          <div className="row align-items-center">
            <div className="col-lg-6 mb-40 text-center text-lg-start">
              <div className="image-feature-2">
                <AnimatedImage
                  className="rounded-5"
                  src="/assets/imgs/page/omx-flow/Automation-01.png"
                  alt="OMX Digital"
                />
              </div>
            </div>
            <div className="col-lg-6 mb-40">
              <AnimatedHeading>
                Automation Builder & 1,000+ Integrations
              </AnimatedHeading>
              <div className="row mt-50">
                <div className="col-lg-6 col-md-6">
                  <AnimatedFeatureCard
                    imageUrl="/assets/imgs/page/omx-flow/settings.gif"
                    text="Trigger automations based on customer actions."
                    index={0}
                  />
                </div>
                <div className="col-lg-6 col-md-6">
                  <AnimatedFeatureCard
                    imageUrl="/assets/imgs/page/omx-flow/shop.gif"
                    text="Connect with Shopify, WooCommerce, CRM, Mailchimp & more for smooth workflows."
                    index={1}
                  />
                </div>
              </div>
            </div>
          </div>
        </AnimatedFeatureBox>

        {/* feature-6 */}
        <AnimatedFeatureBox delay={0.6}>
          <div className="row align-items-center">
            <div className="col-lg-6 mb-40">
              <AnimatedHeading>
                Unified Inbox for WhatsApp, Instagram & Facebook
              </AnimatedHeading>
              <div className="row mt-50">
                <div className="col-lg-6 col-md-6">
                  <AnimatedFeatureCard
                    imageUrl="/assets/imgs/page/omx-flow/assign.gif"
                    text="Assign chats to team members for faster resolutions."
                    index={0}
                  />
                </div>
                <div className="col-lg-6 col-md-6">
                  <AnimatedFeatureCard
                    imageUrl="/assets/imgs/page/omx-flow/message.gif"
                    text="Manage all conversations in one place."
                    index={1}
                  />
                </div>
              </div>
            </div>
            <div className="col-lg-6 mb-40 text-center text-lg-end">
              <div className="image-feature-2">
                <AnimatedImage
                  src="/assets/imgs/page/omx-flow/Unified Inbox-01.png"
                  alt="OMX Digital"
                />
              </div>
            </div>
          </div>
        </AnimatedFeatureBox>
      </div>
    </section>
  );
}
