"use client";
import VideoPopup from "@/components/elements/VideoPopup";
import { Check } from "lucide-react";
import Link from "next/link";
import { motion } from "framer-motion";

export default function Section1() {
  return (
    <>
      <section className="section-box">
        <div className="banner-hero hero-4">
          <div className="banner-inner">
            <div className="container">
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Link className="btn btn-brand-5" href="#">
                  OMX Flow
                </Link>
              </motion.div>

              <motion.h1
                className="display-2 mb-40 mt-15 neutral-0"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.2 }}
              >
                OMX Flow – AI-Powered
                <br />
                WhatsApp API & Chatbot Platform
              </motion.h1>

              <motion.p
                className="text-lg neutral-500 mb-55"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.4 }}
              >
                Automate Conversations, Boost Sales & Scale Customer Support
                24/7
              </motion.p>

              <motion.ul
                className="text-md neutral-500 mb-55"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.7, delay: 0.6 }}
              >
                <motion.li
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.7 }}
                >
                  <Check size={18} /> AI Chatbots for WhatsApp, Instagram &
                  Facebook
                </motion.li>
                <motion.li
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                >
                  <Check size={18} /> Human + AI Support for Customer Queries
                </motion.li>
                <motion.li
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.9 }}
                >
                  <Check size={18} /> Bulk Marketing, Payments & CRM
                  Integrations
                </motion.li>
              </motion.ul>

              <motion.div
                className="box-buttons justify-content-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 1.1 }}
              >
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link className="btn btn-brand-4-medium mr-15" href="https://login.omxsales.com/widget/form/680cd425973e8">
                    Book Demo
                    <svg
                      width={22}
                      height={22}
                      viewBox="0 0 22 22"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M22 11.0003L18.4791 7.47949V10.3074H0V11.6933H18.4791V14.5213L22 11.0003Z"
                        fill="true"
                      />
                    </svg>
                  </Link>
                </motion.div>
                {/* <VideoPopup /> */}
              </motion.div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
