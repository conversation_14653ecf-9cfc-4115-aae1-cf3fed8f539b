import Link from "next/link";

export default function Section3() {
  return (
    <>
      <section className="section-box wow animate__animated animate__fadeIn box-our-features-5 pt-0">
        <div className="container">
          <div className="text-center">
            <Link className="btn btn-brand-5" href="#">
              Problems We Solve
            </Link>
            <h2 className="mb-25 mt-15 neutral-0">
              Problems Faced by Businesses
              <br className="d-none d-lg-block" />
              (And How OMX Flow Solves Them)
            </h2>
            <p className=" neutral-500 mb-55">
              We provide an end-to-end solution for complete tracking of your
              staff, so you don't have to!
              {/* <br className="d-none d-lg-block" />
              understanding of what is going on in the market. Your helpful
              advice can also be beneficial for audiences,
              <br className="d-none d-lg-block" />
              which keeps them coming back for more. */}
            </p>
          </div>
          <div className="row">
            <div className="col-lg-4 col-sm-6">
              <div className="card-features-5">
                <div className="card-image">
                  {/* <img src="/assets/imgs/page/homepage2/web.svg" alt="Nivia" /> */}
                </div>
                <div className="card-info">
                  <div className="d-flex gap-4">
                    <h6>📉</h6>
                    <h6 className="neutral-500">
                      Problem: Missed Sales & Customer Queries After Business
                      Hours
                    </h6>
                  </div>

                  <div className="d-flex gap-4">
                    <h6>✅</h6>
                    <h6 className="neutral-200">
                      Solution: AI-powered 24/7 WhatsApp Chatbot to handle
                      queries, take orders, and qualify leads.
                    </h6>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-4 col-sm-6">
              <div className="card-features-5">
                <div className="card-image">
                  {/* <img src="/assets/imgs/page/homepage2/3d.svg" alt="Nivia" /> */}
                </div>
                <div className="card-info">
                  <div className="d-flex gap-4">
                    <h6>⌛</h6>
                    <h6 className="neutral-500">
                      Problem: Slow & Inefficient Customer Support
                    </h6>
                  </div>
                  <div className="d-flex gap-4">
                    <h6>✅</h6>
                    <h6 className="neutral-200">
                      Solution: AI + Human Agent Hybrid System – Let AI handle
                      FAQs while agents focus on complex queries.
                    </h6>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-4 col-sm-6">
              <div className="card-features-5">
                <div className="card-image">
                  {/* <img
                    src="/assets/imgs/page/homepage2/social.svg"
                    alt="Nivia"
                  /> */}
                </div>
                <div className="card-info">
                  <div className="d-flex gap-4">
                    <h6>🚀</h6>
                    <h6 className="neutral-500">
                      Problem: Low Engagement on WhatsApp & Instagram
                    </h6>
                  </div>
                  <div className="d-flex gap-4">
                    <h6>✅</h6>
                    <h6 className="neutral-200">
                      Solution: Automate DMs, Comments, and Replies across
                      WhatsApp, Instagram & Facebook.
                    </h6>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-4 col-sm-6">
              <div className="card-features-5">
                <div className="card-image">
                  {/* <img
                    src="/assets/imgs/page/homepage2/engine.svg"
                    alt="Nivia"
                  /> */}
                </div>
                <div className="card-info">
                  <div className="d-flex gap-4">
                    <h6>💰</h6>
                    <h6 className="neutral-500">
                      Problem: Manual Order Processing & Payments
                    </h6>
                  </div>
                  <div className="d-flex gap-4">
                    <h6>✅</h6>
                    <h6 className="neutral-200">
                      Solution: WhatsApp Catalog & Native Payment Integration –
                      Customers can browse and pay directly via chat.
                    </h6>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-4 col-sm-6">
              <div className="card-features-5">
                <div className="card-image">
                  {/* <img
                    src="/assets/imgs/page/homepage2/security.svg"
                    alt="Nivia"
                  /> */}
                </div>
                <div className="card-info">
                  <div className="d-flex gap-4">
                    <h6>📩</h6>
                    <h6 className="neutral-500">
                      Problem: Ineffective Marketing & Low Open Rates on
                      Email/SMS
                    </h6>
                  </div>
                  <div className="d-flex gap-4">
                    <h6>✅</h6>
                    <h6 className="neutral-200">
                      Solution: Bulk WhatsApp Campaigns – Reach 98% open rates
                      with AI-driven messaging.
                    </h6>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-4 col-sm-6">
              <div className="card-features-5">
                <div className="card-image">
                  {/* <img
                    src="/assets/imgs/page/homepage2/security.svg"
                    alt="Nivia"
                  /> */}
                </div>
                <div className="card-info">
                  <div className="d-flex gap-4">
                    <h6>🔄</h6>
                    <h6 className="neutral-500">
                      Problem: Lack of Automation & Business Integrations
                    </h6>
                  </div>
                  <div className="d-flex gap-4">
                    <h6>✅</h6>
                    <h6 className="neutral-200">
                      Solution: Connect OMX Flow with 1,000+ Apps like Shopify,
                      WooCommerce, Mailchimp & more.
                    </h6>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* <div className="text-center mt-20">
            <p className="text-md neutral-0">
              Challenges are just opportunities in disguise
              <Link className="brand-4" href="#">
                Take the challenge!
              </Link>
            </p>
          </div> */}
        </div>
      </section>
    </>
  );
}
