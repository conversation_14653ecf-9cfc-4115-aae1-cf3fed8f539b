import VideoPopup from "@/components/elements/VideoPopup";
import FlowCTA from "@/components/elements/FlowCTA";
import Link from "next/link";

export default function Who() {
  return (
    <>
      <section className="section-box wow animate__animated animate__fadeIn box-what-we-do">
        <div className="container">
          <div className="text-center">
            <Link className="btn btn-brand-5" href="#">
              Our Users
            </Link>
            <h2 className="heading-1 neutral-0 mt-15 mb-65">
              Who is OMX Flow For?
            </h2>
          </div>
          <div className="box-list-we-do">
            <div className="row">
              <div className="col-xl-3 col-lg-5">
                <div className="card-features-6 hover-up">
                  {/* <div className="card-image">
                    <img
                      src="/assets/imgs/page/homepage4/web.png"
                      alt="OMX Digital"
                    />
                  </div> */}
                  <div className="card-info">
                    <h6>E-commerce & D2C Brands</h6>
                    <p className="text-sm neutral-400">
                      Automate orders, recover abandoned carts & run marketing.
                    </p>
                    <img
                      className="mt-25"
                      src="/assets/imgs/page/homepage4/img-web.png"
                      alt="OMX Digital"
                    />
                  </div>
                </div>
              </div>
              <div className="col-xl-4 col-lg-7">
                <div className="card-features-6 hover-up">
                  <div className="card-image">
                    {/* <img
                      src="/assets/imgs/page/homepage4/global.png"
                      alt="OMX Digital"
                    /> */}
                  </div>
                  <div className="card-info">
                    <h6>Service Businesses</h6>
                    <p className="text-sm neutral-400">
                      Qualify leads, book appointments & offer AI-powered
                      support.
                    </p>
                  </div>
                </div>
                <div className="row">
                  <div className="col-lg-6">
                    <div className="card-features-6 card-features-300 hover-up">
                      <div className="card-image">
                        {/* <img
                          src="/assets/imgs/page/homepage4/search.png"
                          alt="OMX Digital"
                        /> */}
                      </div>
                      <div className="card-info">
                        <h6>Agencies & Marketers</h6>
                        <p className="text-sm neutral-400">
                          Manage multiple client accounts, run campaigns &
                          automate replies.
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-6">
                    <div className="card-features-6 card-features-300 hover-up">
                      <div className="card-image">
                        {/* <img
                          src="/assets/imgs/page/homepage4/cloud.png"
                          alt="OMX Digital"
                        /> */}
                      </div>
                      <div className="card-info">
                        <h6>Banking & Finance</h6>
                        <p className="text-sm neutral-400">
                          Pre-qualify leads, automate loan inquiries, and
                          provide 24/7 AI-driven customer support.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-xl-5 col-lg-12">
                <div className="row">
                  <div className="col-lg-6">
                    <div className="card-features-6 card-features-300 hover-up">
                      <div className="card-image">
                        {/* <img
                          src="/assets/imgs/page/homepage4/search.png"
                          alt="OMX Digital"
                        /> */}
                      </div>
                      <div className="card-info">
                        <h6>Education & Coaching </h6>
                        <p className="text-sm neutral-400">
                          Automate student inquiries, send updates & collect
                          payments.
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-6">
                    <div className="card-features-6 card-features-300 hover-up">
                      <div className="card-image">
                        {/* <img
                          src="/assets/imgs/page/homepage4/cloud.png"
                          alt="OMX Digital"
                        /> */}
                      </div>
                      <div className="card-info">
                        <h6>Real Estate & Financial Services</h6>
                        <p className="text-sm neutral-400">
                          Pre-qualify leads, schedule site visits & automate
                          document sharing.
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-6">
                    <div className="card-features-6 card-features-300 hover-up">
                      <div className="card-image">
                        {/* <img
                          src="/assets/imgs/page/homepage4/search.png"
                          alt="OMX Digital"
                        /> */}
                      </div>
                      <div className="card-info">
                        <h6>Automotive Industry</h6>
                        <p className="text-sm neutral-400">
                          Automate test drive bookings, service reminders, and
                          customer inquiries.
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-6">
                    <div className="card-features-6 card-features-300 hover-up">
                      <div className="card-image">
                        {/* <img
                          src="/assets/imgs/page/homepage4/cloud.png"
                          alt="OMX Digital"
                        /> */}
                      </div>
                      <div className="card-info">
                        <h6>Restaurants & Food Business</h6>
                        <p className="text-sm neutral-400">
                          Enable WhatsApp ordering, automate reservations, and
                          send promotional offers.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="box-buttons justify-content-center mt-35">
           <FlowCTA />
            {/* <VideoPopup /> */}
          </div>
        </div>
      </section>
    </>
  );
}
