"use client";
import Link from "next/link";
import { useState, useRef } from "react";
import { motion } from "framer-motion";
import { useInView } from "framer-motion";

const Faq = () => {
  const [isActive, setIsActive] = useState({
    status: false,
    key: 1,
  });

  // Refs for scroll detection
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const accordionRef = useRef(null);

  // Check if elements are in view
  const isSectionInView = useInView(sectionRef, { once: true, amount: 0.1 });
  const isTitleInView = useInView(titleRef, { once: true, amount: 0.5 });
  const isAccordionInView = useInView(accordionRef, {
    once: true,
    amount: 0.2,
  });

  const handleClick = (key) => {
    if (isActive.key === key) {
      setIsActive({
        status: false,
      });
    } else {
      setIsActive({
        status: true,
        key,
      });
    }
  };

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.8 } },
  };

  const titleAnimation = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.7, ease: "easeOut" },
    },
  };

  const staggerAccordion = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const accordionItemAnimation = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  // Animation for accordion content
  const contentAnimation = {
    hidden: { opacity: 0, height: 0 },
    visible: {
      opacity: 1,
      height: "auto",
      transition: {
        duration: 0.3,
        ease: "easeInOut",
      },
    },
  };

  return (
    <>
      <motion.section
        id="faq"
        ref={sectionRef}
        initial="hidden"
        animate={isSectionInView ? "visible" : "hidden"}
        variants={fadeIn}
        className="section-box box-faqs-4"
      >
        <div className="container pt-50 ">
          <div className="box-faqs-inner">
            <motion.div
              ref={titleRef}
              initial="hidden"
              animate={isTitleInView ? "visible" : "hidden"}
              variants={titleAnimation}
              className="text-center"
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link className="btn btn-brand-4-sm" href="#">
                  Frequently Asked Questions
                </Link>
              </motion.div>
              <h2 className="heading-2 mb-20 mt-20">
                Do you have any questions?
              </h2>
            </motion.div>

            <motion.div
              ref={accordionRef}
              initial="hidden"
              animate={isAccordionInView ? "visible" : "hidden"}
              variants={staggerAccordion}
              className="box-faqs-inner-4"
            >
              <div
                className="accordion accordion-flush accordion-style-2"
                id="accordionFAQS"
              >
                <motion.div
                  variants={accordionItemAnimation}
                  className="accordion-item"
                >
                  <motion.h2
                    className="accordion-header"
                    id="flush-headingOne"
                    onClick={() => handleClick(1)}
                    
                    whileTap={{ scale: 0.98 }}
                  >
                    <button
                      className={
                        isActive.key == 1
                          ? "accordion-button "
                          : "accordion-button collapsed"
                      }
                      type="button"
                      data-bs-toggle="collapse"
                      data-bs-target="#flush-collapseOne"
                      aria-expanded="false"
                      aria-controls="flush-collapseOne"
                    >
                      Can I integrate OMX Sales with my existing tools?
                    </button>
                  </motion.h2>
                  <motion.div
                    animate={isActive.key == 1 ? "visible" : "hidden"}
                    variants={contentAnimation}
                    className={
                      isActive.key == 1
                        ? "accordion-collapse collapse show"
                        : "accordion-collapse collapse"
                    }
                    id="flush-collapseOne"
                    aria-labelledby="flush-headingOne"
                    data-bs-parent="#accordionFAQS"
                  >
                    <div className="accordion-body">
                      <p>
                        Yes! It supports third party integrations via APIs and
                        Webhooks.
                      </p>
                    </div>
                  </motion.div>
                </motion.div>

                <motion.div
                  variants={accordionItemAnimation}
                  className="accordion-item"
                >
                  <motion.h2
                    className="accordion-header"
                    id="flush-headingTwo"
                    onClick={() => handleClick(2)}
                    
                    whileTap={{ scale: 0.98 }}
                  >
                    <button
                      className={
                        isActive.key == 2
                          ? "accordion-button "
                          : "accordion-button collapsed"
                      }
                      type="button"
                      data-bs-toggle="collapse"
                      data-bs-target="#flush-collapseTwo"
                      aria-expanded="false"
                      aria-controls="flush-collapseTwo"
                    >
                      How does Cloud Calling work?{" "}
                    </button>
                  </motion.h2>
                  <motion.div
                    animate={isActive.key == 2 ? "visible" : "hidden"}
                    variants={contentAnimation}
                    className={
                      isActive.key == 2
                        ? "accordion-collapse collapse show"
                        : "accordion-collapse collapse"
                    }
                    id="flush-collapseTwo"
                    aria-labelledby="flush-headingTwo"
                    data-bs-parent="#accordionFAQS"
                  >
                    <div className="accordion-body">
                      You can make & receive calls directly inside OMX Sales,
                      with call tracking & logging.
                    </div>
                  </motion.div>
                </motion.div>

                <motion.div
                  variants={accordionItemAnimation}
                  className="accordion-item"
                >
                  <motion.h2
                    className="accordion-header"
                    id="flush-headingThree"
                    onClick={() => handleClick(3)}
                    
                    whileTap={{ scale: 0.98 }}
                  >
                    <button
                      className={
                        isActive.key == 3
                          ? "accordion-button "
                          : "accordion-button collapsed"
                      }
                      type="button"
                      data-bs-toggle="collapse"
                      data-bs-target="#flush-collapseThree"
                      aria-expanded="false"
                      aria-controls="flush-collapseThree"
                    >
                      Can I use OMX Sales for WhatsApp marketing?
                    </button>
                  </motion.h2>
                  <motion.div
                    animate={isActive.key == 3 ? "visible" : "hidden"}
                    variants={contentAnimation}
                    className={
                      isActive.key == 3
                        ? "accordion-collapse collapse show"
                        : "accordion-collapse collapse"
                    }
                    id="flush-collapseThree"
                    aria-labelledby="flush-headingThree"
                    data-bs-parent="#accordionFAQS"
                  >
                    <div className="accordion-body">
                      Yes, we offer WhatsApp API automation & chatbot
                      integrations.
                    </div>
                  </motion.div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.section>
    </>
  );
};

export default Faq;
