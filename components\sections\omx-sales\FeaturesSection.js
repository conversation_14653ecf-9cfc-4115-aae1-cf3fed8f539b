"use client";
import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";
import Link from "next/link";

// Custom animated feature box component
const AnimatedFeatureBox = ({
  title,
  image,
  isImageRight = false,
  children,
  className = "",
}) => {
  const boxRef = useRef(null);
  const isInView = useInView(boxRef, { once: false, amount: 0.2 });

  return (
    <motion.div
      ref={boxRef}
      initial={{ opacity: 0 }}
      animate={isInView ? { opacity: 1 } : { opacity: 0 }}
      transition={{ duration: 0.5 }}
      className={`box-border-rounded ${className}`}
    >
      <div className="row align-items-center">
        {!isImageRight && (
          <motion.div
            className="col-lg-6 mb-40 text-center text-lg-start"
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.7, delay: 0.2 }}
          >
            <div className="image-feature-2">
              <img className="rounded-5" src={image} alt="omx-digital" />
            </div>
          </motion.div>
        )}

        <motion.div
          className="col-lg-6 mb-40"
          initial={{ opacity: 0, x: isImageRight ? -30 : 30 }}
          animate={
            isInView
              ? { opacity: 1, x: 0 }
              : { opacity: 0, x: isImageRight ? -30 : 30 }
          }
          transition={{ duration: 0.7, delay: 0.2 }}
        >
          <motion.h3
            className="heading-3 mb-20"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            {title}
          </motion.h3>
          <motion.div
            className="row mt-50"
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : { opacity: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            {children}
          </motion.div>
        </motion.div>

        {isImageRight && (
          <motion.div
            className="col-lg-6 mb-40 text-center text-lg-end"
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
            transition={{ duration: 0.7, delay: 0.2 }}
          >
            <div className="image-feature-2">
              <img src={image} alt="omx-digital" />
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

// Animated card component for the feature items
const AnimatedFeatureCard = ({ image, text, delay = 0 }) => {
  const cardRef = useRef(null);
  const isInView = useInView(cardRef, { once: false, amount: 0.5 });

  return (
    <div className="col-lg-6 col-md-6">
      <motion.div
        ref={cardRef}
        className="card-feature-2"
        initial={{ opacity: 0, y: 30 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
        transition={{ duration: 0.5, delay: delay }}
      >
        <motion.div
          className="card-image"
          initial={{ scale: 0.8 }}
          animate={isInView ? { scale: 1 } : { scale: 0.8 }}
          transition={{ duration: 0.5, delay: delay + 0.1 }}
        >
          <img src={image} />
        </motion.div>
        <div className="card-info">
          <motion.p
            className="text-md neutral-700"
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : { opacity: 0 }}
            transition={{ duration: 0.5, delay: delay + 0.2 }}
          >
            {text}
          </motion.p>
        </div>
      </motion.div>
    </div>
  );
};

// The main Features Section component
export default function FeaturesSection() {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: false, amount: 0.1 });

  return (
    <section
      id={'features'}
      ref={sectionRef}
      className="section-box box-benifit box-our-working"
      style={{
        background: "linear-gradient(to bottom,rgb(193, 243, 209), #ffffff)",
      }}
    >
      <motion.div
        className="container"
        initial={{ opacity: 0 }}
        animate={isInView ? { opacity: 1 } : { opacity: 0 }}
        transition={{ duration: 0.8 }}
      >
        {/* feature-1 */}
        <AnimatedFeatureBox
          title="AI-Powered Ad Launcher"
          image="/assets/imgs/page/omx-sales/ad-launcher.png"
        >
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-flow/ad.gif"
            text="Run high-converting ads on Facebook, Instagram, Google & LinkedIn."
            delay={0.2}
          />
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-flow/aibot.gif"
            text="AI-based audience targeting & budget optimization."
            delay={0.3}
          />
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-sales/optimize.gif"
            text="Track lead sources & optimize ad performance."
            delay={0.4}
          />
        </AnimatedFeatureBox>

        {/* feature-2 */}
        <AnimatedFeatureBox
          title="CRM & Lead Management"
          image="/assets/imgs/page/omx-sales/crm.png"
          isImageRight={true}
        >
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-sales/adclick.gif"
            text="One-click lead capture from ads, forms & calls."
            delay={0.2}
          />
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-flow/settings.gif"
            text="Auto-assign leads to sales reps based on rules."
            delay={0.3}
          />
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-sales/task.gif"
            text="Track every interaction (WhatsApp, SMS, Email, Calls)."
            delay={0.4}
          />
        </AnimatedFeatureBox>
      </motion.div>

      {/* Container for remaining features */}
      <div className="container">
        {/* feature-3 */}
        <AnimatedFeatureBox
          title="WhatsApp API, SMS, & Email Automation"
          image="/assets/imgs/page/omx-sales/whatsapp.png"
        >
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-flow/settings.gif"
            text="Send automated follow-ups to leads."
            delay={0.2}
          />
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-sales/whats.gif"
            text="Trigger WhatsApp messages based on user actions."
            delay={0.3}
          />
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-flow/message.gif"
            text="Bulk messaging & drip campaigns for better engagement."
            delay={0.4}
          />
        </AnimatedFeatureBox>

        {/* feature-4 */}
        <AnimatedFeatureBox
          title="Cloud Calling Solution (Telephony Integration)"
          image="/assets/imgs/page/omx-sales/irv.jpg"
          isImageRight={true}
        >
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-sales/cloud.gif"
            text="Call leads directly from OMX Sales."
            delay={0.2}
          />
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-sync/chart_16045892.gif"
            text="Auto-record & track call performance."
            delay={0.3}
          />
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-sync/questionnaire_12134209.gif"
            text="Assign calls to team members & set reminders."
            delay={0.4}
          />
        </AnimatedFeatureBox>

        {/* feature-5 */}
        <AnimatedFeatureBox
          title="Calendar & Appointments"
          image="/assets/imgs/page/omx-sales/calander.png"
        >
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-sync/bell.gif"
            text="Automated meeting scheduling & reminders."
            delay={0.2}
          />
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-sync/calendar_17110627.gif"
            text="Integrate with Google Calendar & Outlook."
            delay={0.3}
          />
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-sync/time-managament_17905442.gif"
            text="Reduce no-shows with WhatsApp & SMS reminders."
            delay={0.4}
          />
        </AnimatedFeatureBox>

        {/* feature-6 */}
        <AnimatedFeatureBox
          title="Unified Inbox (All Conversations in One Place)"
          image="/assets/imgs/page/omx-sales/inbox.png"
          isImageRight={true}
        >
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-sales/cloud.gif"
            text="WhatsApp, Email, SMS, and Cloud Calls – in a single dashboard."
            delay={0.2}
          />
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-flow/resend.gif"
            text="Assign chats to sales reps for faster responses."
            delay={0.3}
          />
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-sync/analytics_19005382.gif"
            text="Monitor and analyze team communication."
            delay={0.4}
          />
        </AnimatedFeatureBox>

        {/* feature-7 */}
        <AnimatedFeatureBox
          title="UTM Analytics & Reporting"
          image="/assets/imgs/page/omx-sales/utm.png"
        >
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-sync/search-engine_18559540.gif"
            text="See which ads, platforms & sources bring the best leads."
            delay={0.2}
          />
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-sync/idea_18830547.gif"
            text="AI-powered insights for data-driven marketing decisions."
            delay={0.3}
          />
          <AnimatedFeatureCard
            image="/assets/imgs/page/omx-sync/chart_16045892.gif"
            text="Custom reports to track sales team performance."
            delay={0.4}
          />
        </AnimatedFeatureBox>
      </div>
    </section>
  );
}
