"use client";
import VideoPopup from "@/components/elements/VideoPopup";
import SalesCTA from "@/components/elements/SalesCTA";
import { Check } from "lucide-react";
import Link from "next/link";
import { motion, useScroll } from "framer-motion";
import { useRef, useEffect } from "react";

export default function Section1() {
  // Reference for the section
  const sectionRef = useRef(null);

  // Variants for different animations
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { duration: 0.6 },
    },
  };

  const slideUp = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.7, ease: "easeOut" },
    },
  };

  const slideInLeft = {
    hidden: { x: -50, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: { duration: 0.5, ease: "easeOut" },
    },
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const checkmarkBounce = {
    hidden: { scale: 0, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 15,
      },
    },
  };

  const buttonHover = {
    hover: {
      scale: 1.05,
      transition: { duration: 0.2 },
    },
  };

  // Custom hook to determine if element is in view
  function useInView(ref) {
    const [isInView, setIsInView] = useState(false);

    useEffect(() => {
      const observer = new IntersectionObserver(
        ([entry]) => {
          setIsInView(entry.isIntersecting);
        },
        { threshold: 0.1 }
      );

      if (ref.current) {
        observer.observe(ref.current);
      }

      return () => {
        if (ref.current) {
          observer.unobserve(ref.current);
        }
      };
    }, [ref]);

    return isInView;
  }

  // Import useState if not already imported
  const { useState } = require("react");
  const isInView = useInView(sectionRef);

  return (
    <>
      <motion.section
        ref={sectionRef}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="section-box"
      >
        <div className="banner-hero hero-4">
          <div className="banner-inner">
            <div className="container">
              <motion.div variants={fadeIn}>
                <Link className="btn btn-brand-5" href="#">
                  OMX Sales
                </Link>
              </motion.div>

              <motion.h1
                variants={slideUp}
                className="display-2 mb-40 mt-15 neutral-0"
              >
                The Only Sales & Marketing
                <br />
                Automation Platform You Need!
              </motion.h1>

              <motion.p
                variants={slideUp}
                className="text-lg neutral-500 mb-55"
              >
                Run Ads, Capture Leads, Automate Workflows, and Manage Clients –
                All in One Place.
              </motion.p>

              <motion.ul
                variants={staggerContainer}
                className="text-md neutral-500 mb-55"
              >
                <motion.li variants={slideInLeft}>
                  <motion.span variants={checkmarkBounce}>
                    <Check size={18} />
                  </motion.span>{" "}
                  AI-powered lead generation & nurturing
                </motion.li>
                <motion.li variants={slideInLeft}>
                  <motion.span variants={checkmarkBounce}>
                    <Check size={18} />
                  </motion.span>{" "}
                  WhatsApp API, Cloud Calling, SMS & Email Automation
                </motion.li>
                <motion.li variants={slideInLeft}>
                  <motion.span variants={checkmarkBounce}>
                    <Check size={18} />
                  </motion.span>{" "}
                  Centralized CRM, Ad Launcher, and Lead Tracking
                </motion.li>
              </motion.ul>

              <motion.div
                variants={fadeIn}
                className="box-buttons justify-content-center"
              >
                {/* <SalesCTA /> */}
                <Link className="btn btn-brand-4-medium hover-up" href="/form">
                Book a Demo
                <svg
                  width={22}
                  height={22}
                  viewBox="0 0 22 22"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M22 11.0003L18.4791 7.47949V10.3074H0V11.6933H18.4791V14.5213L22 11.0003Z"
                    fill="true"
                  ></path>
                </svg>
              </Link>

                {/* <motion.div
                  variants={fadeIn}
                  whileHover="hover"
                  variants={buttonHover}
                >
                  <VideoPopup />
                </motion.div> */}
              </motion.div>
            </div>
          </div>
        </div>
      </motion.section>
    </>
  );
}
