"use client";
import Link from "next/link";
import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

// Custom component for animated elements
const AnimatedElement = ({ children, delay = 0, className }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: false, amount: 0.3 });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.5, delay: delay }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

export default function Section3() {
  return (
    <>
      <section className="section-box wow box-our-features-5 pt-0">
        <div className="container">
          <div className="text-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: false, amount: 0.3 }}
              transition={{ duration: 0.5 }}
            >
              <Link className="btn btn-brand-5" href="#">
                Problems We Solve
              </Link>
            </motion.div>

            <motion.h2
              className="mb-25 mt-15 neutral-0"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: false, amount: 0.3 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Problems Faced by Businesses
              <br className="d-none d-lg-block" />
              (And How OMX Sales Solves Them)
            </motion.h2>

            <motion.p
              className="neutral-500 mb-55"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: false, amount: 0.3 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              We provide an end-to-end solution for complete tracking of your
              staff, so you don't have to!
            </motion.p>
          </div>

          <div className="row">
            {/* Card 1 */}
            <div className="col-lg-4 col-sm-6">
              <AnimatedElement delay={0.1} className="card-features-5">
                <div className="card-image">{/* Image content */}</div>
                <div className="card-info">
                  <div className="d-flex gap-4">
                    <motion.h6
                      initial={{ scale: 0.5 }}
                      whileInView={{ scale: 1 }}
                      viewport={{ once: false }}
                      transition={{ duration: 0.3 }}
                    >
                      🎯
                    </motion.h6>
                    <h6 className="neutral-500">
                      Struggling with Lead Generation & Follow-Ups
                    </h6>
                  </div>

                  <div className="d-flex gap-4">
                    <motion.h6
                      initial={{ scale: 0.5 }}
                      whileInView={{ scale: 1 }}
                      viewport={{ once: false }}
                      transition={{ duration: 0.3, delay: 0.2 }}
                    >
                      ✅
                    </motion.h6>
                    <h6 className="neutral-200">
                      Solution: AI-powered Ad Launcher + CRM + WhatsApp
                      Automations ensure no lead is wasted.
                    </h6>
                  </div>
                </div>
              </AnimatedElement>
            </div>

            {/* Card 2 */}
            <div className="col-lg-4 col-sm-6">
              <AnimatedElement delay={0.2} className="card-features-5">
                <div className="card-image">{/* Image content */}</div>
                <div className="card-info">
                  <div className="d-flex gap-4">
                    <motion.h6
                      initial={{ scale: 0.5 }}
                      whileInView={{ scale: 1 }}
                      viewport={{ once: false }}
                      transition={{ duration: 0.3 }}
                    >
                      📞
                    </motion.h6>
                    <h6 className="neutral-500">
                      Problem: Missed or Untracked Calls & Conversations
                    </h6>
                  </div>
                  <div className="d-flex gap-4">
                    <motion.h6
                      initial={{ scale: 0.5 }}
                      whileInView={{ scale: 1 }}
                      viewport={{ once: false }}
                      transition={{ duration: 0.3, delay: 0.2 }}
                    >
                      ✅
                    </motion.h6>
                    <h6 className="neutral-200">
                      Solution: Cloud Calling Integration – Auto-log calls,
                      assign to the right team, and track call performance.
                    </h6>
                  </div>
                </div>
              </AnimatedElement>
            </div>

            {/* Card 3 */}
            <div className="col-lg-4 col-sm-6">
              <AnimatedElement delay={0.3} className="card-features-5">
                <div className="card-image">{/* Image content */}</div>
                <div className="card-info">
                  <div className="d-flex gap-4">
                    <motion.h6
                      initial={{ scale: 0.5 }}
                      whileInView={{ scale: 1 }}
                      viewport={{ once: false }}
                      transition={{ duration: 0.3 }}
                    >
                      🚀
                    </motion.h6>
                    <h6 className="neutral-500">
                      Problem: Slow Lead Response & Low Conversions
                    </h6>
                  </div>
                  <div className="d-flex gap-4">
                    <motion.h6
                      initial={{ scale: 0.5 }}
                      whileInView={{ scale: 1 }}
                      viewport={{ once: false }}
                      transition={{ duration: 0.3, delay: 0.2 }}
                    >
                      ✅
                    </motion.h6>
                    <h6 className="neutral-200">
                      Solution: AI-driven WhatsApp, SMS, and Email automation
                      for instant follow-ups and higher conversions.
                    </h6>
                  </div>
                </div>
              </AnimatedElement>
            </div>

            {/* Card 4 */}
            <div className="col-lg-4 col-sm-6">
              <AnimatedElement delay={0.4} className="card-features-5">
                <div className="card-image">{/* Image content */}</div>
                <div className="card-info">
                  <div className="d-flex gap-4">
                    <motion.h6
                      initial={{ scale: 0.5 }}
                      whileInView={{ scale: 1 }}
                      viewport={{ once: false }}
                      transition={{ duration: 0.3 }}
                    >
                      📊
                    </motion.h6>
                    <h6 className="neutral-500">
                      Problem: No Clear Insight Into Marketing & Sales Efforts
                    </h6>
                  </div>
                  <div className="d-flex gap-4">
                    <motion.h6
                      initial={{ scale: 0.5 }}
                      whileInView={{ scale: 1 }}
                      viewport={{ once: false }}
                      transition={{ duration: 0.3, delay: 0.2 }}
                    >
                      ✅
                    </motion.h6>
                    <h6 className="neutral-200">
                      Solution: UTM Analytics & Lead Tracking – See which
                      campaigns drive results in real-time.
                    </h6>
                  </div>
                </div>
              </AnimatedElement>
            </div>

            {/* Card 5 */}
            <div className="col-lg-4 col-sm-6">
              <AnimatedElement delay={0.5} className="card-features-5">
                <div className="card-image">{/* Image content */}</div>
                <div className="card-info">
                  <div className="d-flex gap-4">
                    <motion.h6
                      initial={{ scale: 0.5 }}
                      whileInView={{ scale: 1 }}
                      viewport={{ once: false }}
                      transition={{ duration: 0.3 }}
                    >
                      💬
                    </motion.h6>
                    <h6 className="neutral-500">
                      Problem: Managing Multiple Communication Channels
                    </h6>
                  </div>
                  <div className="d-flex gap-4">
                    <motion.h6
                      initial={{ scale: 0.5 }}
                      whileInView={{ scale: 1 }}
                      viewport={{ once: false }}
                      transition={{ duration: 0.3, delay: 0.2 }}
                    >
                      ✅
                    </motion.h6>
                    <h6 className="neutral-200">
                      Solution: Centralized Inbox – Handle WhatsApp, SMS, Email
                      & Calls from one dashboard.
                    </h6>
                  </div>
                </div>
              </AnimatedElement>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
