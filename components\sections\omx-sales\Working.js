"use client";
import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

export default function Working() {
  // Refs for scroll detection
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const imageTopRef = useRef(null);
  const imageBottomRef = useRef(null);
  const stepsRef = useRef(null);

  // Check if elements are in view
  const isSectionInView = useInView(sectionRef, { once: true, amount: 0.1 });
  const isTitleInView = useInView(titleRef, { once: true, amount: 0.5 });
  const isImageTopInView = useInView(imageTopRef, { once: true, amount: 0.5 });
  const isImageBottomInView = useInView(imageBottomRef, {
    once: true,
    amount: 0.5,
  });
  const isStepsInView = useInView(stepsRef, { once: true, amount: 0.2 });

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.8 } },
  };

  const titleAnimation = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.7, ease: "easeOut" },
    },
  };

  const imageAnimation = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  const staggerSteps = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        delayChildren: 0.2,
      },
    },
  };

  const stepAnimation = {
    hidden: { opacity: 0, x: -30 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <>
      <motion.section
        ref={sectionRef}
        initial="hidden"
        animate={isSectionInView ? "visible" : "hidden"}
        variants={fadeIn}
        className="section-box box-section-5"
      >
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-5 mb-40">
              <motion.div
                ref={imageTopRef}
                initial="hidden"
                animate={isImageTopInView ? "visible" : "hidden"}
                variants={imageAnimation}
                className="box-sec5-img-top"
              >
                {/* Chart images commented out */}
              </motion.div>
              <motion.div
                ref={imageBottomRef}
                initial="hidden"
                animate={isImageBottomInView ? "visible" : "hidden"}
                variants={imageAnimation}
                className="box-sec5-img-bottom"
              >
                <motion.img
                  whileHover={{ scale: 1.05, transition: { duration: 0.3 } }}
                  className="shape-3"
                  alt="Nivia"
                  src="/assets/imgs/page/homepage6/chart3.png"
                />
                {/* Second chart image commented out */}
              </motion.div>
            </div>
            <div className="col-lg-7 mb-40">
              <div className="box-info-section5">
                <motion.div
                  ref={titleRef}
                  initial="hidden"
                  animate={isTitleInView ? "visible" : "hidden"}
                  variants={titleAnimation}
                >
                  <span className="text-logo">
                    OMX <span className="neutral-500">Sales</span>
                  </span>
                  <h2 className="display-2 text-linear-3 mb-65">
                    How OMX Sales Works (Simple Process)
                  </h2>
                </motion.div>

                <motion.div
                  ref={stepsRef}
                  initial="hidden"
                  animate={isStepsInView ? "visible" : "hidden"}
                  variants={staggerSteps}
                  className="box-list-steps box-mw-steps"
                >
                  <motion.div
                    variants={stepAnimation}
                    whileHover={{ x: 5, transition: { duration: 0.2 } }}
                    className="item-step"
                  >
                    <div className="step-number">
                      <motion.span
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        1
                      </motion.span>
                    </div>
                    <div className="step-info">
                      <h3>Make Funnels, Run Ads & Capture Leads </h3>
                      {/* Commented paragraph */}
                    </div>
                  </motion.div>

                  <motion.div
                    variants={stepAnimation}
                    whileHover={{ x: 5, transition: { duration: 0.2 } }}
                    className="item-step"
                  >
                    <div className="step-number">
                      <motion.span
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        2
                      </motion.span>
                    </div>
                    <div className="step-info">
                      <h3> Automate Follow-Ups (WhatsApp, SMS, Email)</h3>
                      {/* Commented paragraph */}
                    </div>
                  </motion.div>

                  <motion.div
                    variants={stepAnimation}
                    whileHover={{ x: 5, transition: { duration: 0.2 } }}
                    className="item-step"
                  >
                    <div className="step-number">
                      <motion.span
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        3
                      </motion.span>
                    </div>
                    <div className="step-info">
                      <h3>Close Deals with CRM, Cloud Calling & Analytics </h3>
                      {/* Commented paragraph */}
                    </div>
                  </motion.div>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </motion.section>
    </>
  );
}
