'use client';
import VideoPopup from "@/components/elements/VideoPopup";
import SyncCTA from "@/components/elements/SyncCTA";
import { useInView } from "framer-motion";
import { Check } from "lucide-react";
import Link from "next/link";

export default function Section1() {
  return (
    <>
      <section className="section-box">
        <div className="banner-hero hero-4">
          <div className="banner-inner">
            <div className="container">
              <Link className="btn btn-brand-5" href="#">
                OMX Sync
              </Link>
              <h1 className="display-2 mb-40 mt-15 neutral-0">
                The Ultimate Task & Team
                <br />
                Management Solution
                <br />
                For High-Performance Businesses!
              </h1>
              <p className="text-lg neutral-500 mb-55">
                Assign, Track & Automate Tasks Across Teams – All from One Smart
                Dashboard.
              </p>
              <ul className="text-md neutral-500 mb-55">
                <li>
                  <Check size={18} /> AI-powered task management & automation
                </li>
                <li>
                  <Check size={18} /> Recurring tasks, voice-recorded tasks, &
                  team performance tracking
                </li>
                <li>
                  <Check size={18} /> Expense management, leave tracking &
                  QR-based attendance
                </li>
              </ul>
              <div className="box-buttons justify-content-center">
                <Link className="btn btn-brand-4-medium hover-up" href="/form">
                                Book a Demo
                                <svg
                                  width={22}
                                  height={22}
                                  viewBox="0 0 22 22"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M22 11.0003L18.4791 7.47949V10.3074H0V11.6933H18.4791V14.5213L22 11.0003Z"
                                    fill="true"
                                  ></path>
                                </svg>
                              </Link>
                {/* <SyncCTA /> */}
                {/* <VideoPopup /> */}
              </div>
              {/* <div className="text-center box-image-banner"><img src="/assets/imgs/page/homepage2/banner.png" alt="Omx Digital" /></div> */}
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
