import Link from "next/link";

export default function Section3() {
  return (
    <>
      <section className="section-box wow animate__animated animate__fadeIn box-our-features-5 pt-0">
        <div className="container">
          <div className="text-center">
            <Link className="btn btn-brand-5" href="#">
              Problems We Solve
            </Link>
            <h2 className="mb-25 mt-15 neutral-0">
              Problems Faced by Businesses
              <br className="d-none d-lg-block" />
              (And How OMX Sync Solves Them)
            </h2>
            <p className=" neutral-500 mb-55">
              We provide an end-to-end solution for complete tracking of your
              staff, so you don't have to!
              {/* <br className="d-none d-lg-block" />
              understanding of what is going on in the market. Your helpful
              advice can also be beneficial for audiences,
              <br className="d-none d-lg-block" />
              which keeps them coming back for more. */}
            </p>
          </div>
          <div className="row">
            <div className="col-lg-4 col-sm-6">
              <div className="card-features-5">
                <div className="card-image">
                  {/* <img src="/assets/imgs/page/homepage2/web.svg" alt="Nivia" /> */}
                </div>
                <div className="card-info">
                  <div className="d-flex gap-4">
                    <h6>🚀</h6>
                    <h6 className="neutral-500">
                      <span> Problem:</span> Teams Struggle with Task Visibility
                      & Accountability
                    </h6>
                  </div>

                  <div className="d-flex gap-4">
                    <h6>✅</h6>
                    <h6 className="neutral-200">
                      Solution: Real-time task tracking with automated reminders
                      & follow-ups.
                    </h6>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-4 col-sm-6">
              <div className="card-features-5">
                <div className="card-image">
                  {/* <img src="/assets/imgs/page/homepage2/3d.svg" alt="Nivia" /> */}
                </div>
                <div className="card-info">
                  <div className="d-flex gap-4">
                    <h6>📆</h6>
                    <h6 className="neutral-500">
                      Problem: Repetitive Tasks Are Time-Consuming
                    </h6>
                  </div>
                  <div className="d-flex gap-4">
                    <h6>✅</h6>
                    <h6 className="neutral-200">
                      Solution: Recurring Task Automation – Set once, repeat on
                      schedule.
                    </h6>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-4 col-sm-6">
              <div className="card-features-5">
                <div className="card-image">
                  {/* <img
                    src="/assets/imgs/page/homepage2/social.svg"
                    alt="Nivia"
                  /> */}
                </div>
                <div className="card-info">
                  <div className="d-flex gap-4">
                    <h6>🎤 </h6>
                    <h6 className="neutral-500">
                      Problem: Manual Task Assignments Slow Down Productivity
                    </h6>
                  </div>
                  <div className="d-flex gap-4">
                    <h6>✅</h6>
                    <h6 className="neutral-200">
                      Solution: Voice-Recorded Task Assignments – Assign tasks
                      via speech & automate workflows.
                    </h6>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-4 col-sm-6">
              <div className="card-features-5">
                <div className="card-image">
                  {/* <img
                    src="/assets/imgs/page/homepage2/engine.svg"
                    alt="Nivia"
                  /> */}
                </div>
                <div className="card-info">
                  <div className="d-flex gap-4">
                    <h6>📊 </h6>
                    <h6 className="neutral-500">
                      Problem: No Clear Insights into Team Performance
                    </h6>
                  </div>
                  <div className="d-flex gap-4">
                    <h6>✅</h6>
                    <h6 className="neutral-200">
                      Solution: Performance & Productivity Reports – Identify
                      top performers & bottlenecks.
                    </h6>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-4 col-sm-6">
              <div className="card-features-5">
                <div className="card-image">
                  {/* <img
                    src="/assets/imgs/page/homepage2/security.svg"
                    alt="Nivia"
                  /> */}
                </div>
                <div className="card-info">
                  <div className="d-flex gap-4">
                    <h6>💰</h6>
                    <h6 className="neutral-500">
                      Problem: Unmanaged Expenses & Lack of Financial Oversight
                    </h6>
                  </div>
                  <div className="d-flex gap-4">
                    <h6>✅</h6>
                    <h6 className="neutral-200">
                      Solution: Track expenses with in-built approval workflows
                      & real-time reporting.
                    </h6>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-4 col-sm-6">
              <div className="card-features-5">
                <div className="card-image">
                  {/* <img
                    src="/assets/imgs/page/homepage2/firewall.svg"
                    alt="Nivia"
                  /> */}
                </div>
                <div className="card-info">
                  <div className="d-flex gap-4">
                    <h6>🕒</h6>
                    <h6 className="neutral-500">
                      Problem: Inefficient Leave & Attendance Management
                    </h6>
                  </div>
                  <div className="d-flex gap-4">
                    <h6>✅</h6>
                    <h6 className="neutral-200">
                      Solution: QR-based Attendance System – Ensure real-time &
                      location-based check-ins.
                    </h6>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* <div className="text-center mt-20">
            <p className="text-md neutral-0">
              Challenges are just opportunities in disguise
              <Link className="brand-4" href="#">
                Take the challenge!
              </Link>
            </p>
          </div> */}
        </div>
      </section>
    </>
  );
}
