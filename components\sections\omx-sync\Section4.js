
import Link from 'next/link'

export default function Section4() {
    return (
        <>

            <section className="section-box wow animate__animated animate__fadeIn box-case-study-2">
                <div className="container">
                    <div className="text-center"><Link className="btn btn-brand-4-sm" href="#">Case Studies</Link>
                        <h2 className="mb-35 mt-15">Proud projects that make<br className="d-none d-lg-block" />us stand out</h2>
                    </div>
                    <div className="row align-items-center">
                        <div className="col-lg-5"><img src="/assets/imgs/page/homepage2/img-case.png" alt="Nivia" /></div>
                        <div className="col-lg-7">
                            <div className="box-info-casestudy">
                                <h3 className="mb-40">Find out everything about creating<br className="d-none d-lg-block" />a business
                                    model.</h3>
                                <div className="row">
                                    <div className="col-lg-6">
                                        <div className="card-casestudy">
                                            <div className="card-title">
                                                <h6><span className="number">1</span>Requirement Analysis</h6>
                                            </div>
                                            <div className="card-desc">
                                                <p>The results of data analysis can help organizations make informed
                                                    decisions, identify trends, optimize processes, and gain a competitive
                                                    edge in their respective industries.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="col-lg-6">
                                        <div className="card-casestudy">
                                            <div className="card-title">
                                                <h6><span className="number">2</span>Magic Touch</h6>
                                            </div>
                                            <div className="card-desc">
                                                <p>The Magic Touch can be implemented through various means, such as
                                                    advanced touch gestures, motion sensing, voice recognition, or augmented
                                                    reality.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="col-lg-6">
                                        <div className="card-casestudy">
                                            <div className="card-title">
                                                <h6><span className="number">3</span>Data Analysis</h6>
                                            </div>
                                            <div className="card-desc">
                                                <p>This process typically includes conducting interviews, workshops, and
                                                    surveys to gather information, analyzing existing systems or processes,
                                                    and documenting the requirements in a clear and structured manner.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="col-lg-6">
                                        <div className="card-casestudy">
                                            <div className="card-title">
                                                <h6><span className="number">4</span>Finalize Product</h6>
                                            </div>
                                            <div className="card-desc">
                                                <p>The goal of requirement analysis is to understand the purpose and scope
                                                    of the project, identify the functional and non-functional requirements,
                                                    and prioritize them based on their importance and feasibility.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="text-center mt-130"><Link className="btn btn-brand-4-sm" href="#">Case Studies</Link>
                        <h2 className="mb-75 mt-15">Proud projects that make<br className="d-none d-lg-block" />us stand out</h2>
                    </div>
                    <div className="row align-items-center">
                        <div className="col-lg-5">
                            <div className="box-info-casestudy">
                                <h3 className="mb-40">We bring solutions to make life<br className="d-none d-lg-block" />easier for
                                    our clients.</h3>
                                <div className="row">
                                    <div className="col-lg-12">
                                        <div className="card-casestudy">
                                            <div className="card-title">
                                                <h6><span className="number">1</span>Quality Assurance System</h6>
                                            </div>
                                            <div className="card-desc">
                                                <p>A quality assurance system is a set of processes, procedures, and
                                                    activities implemented within an organization to ensure that products or
                                                    services meet specified quality standards.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="col-lg-12">
                                        <div className="card-casestudy">
                                            <div className="card-title">
                                                <h6><span className="number">2</span>Smart API Generation</h6>
                                            </div>
                                            <div className="card-desc">
                                                <p>Smart API generation involves utilizing advanced tools, frameworks, or
                                                    artificial intelligence techniques to streamline and simplify the API
                                                    development process.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="col-lg-12">
                                        <div className="card-casestudy">
                                            <div className="card-title">
                                                <h6><span className="number">3</span>Accurate Testing Processes</h6>
                                            </div>
                                            <div className="card-desc">
                                                <p> Accurate testing processes aim to identify defects, errors, or
                                                    inconsistencies in the software and provide actionable feedback to
                                                    developers for remediation.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="col-lg-7 text-center"><img src="/assets/imgs/page/homepage2/img-case2.png" alt="Nivia" />
                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}
