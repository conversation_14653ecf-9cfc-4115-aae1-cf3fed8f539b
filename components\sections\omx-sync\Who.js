"use client";
import VideoPopup from "@/components/elements/VideoPopup";
import SyncCTA from "@/components/elements/SyncCTA";
import Link from "next/link";
import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { use, useRef } from "react";

export default function Who() {
  // Refs for scroll detection
  const sectionRef = useRef(null);
  const headerRef = useRef(null);
  const cardsContainerRef = useRef(null);
  const buttonRef = useRef(null);
  
  // Check if elements are in view
  const isSectionInView = useInView(sectionRef, { once: true, amount: 0.1 });
  const isHeaderInView = useInView(headerRef, { once: true, amount: 0.5 });
  const isCardsInView = useInView(cardsContainerRef, { once: true, amount: 0.2 });
  const isButtonInView = useInView(buttonRef, { once: true, amount: 0.5 });

  // Animation variants
  const sectionVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        duration: 0.7,
        staggerChildren: 0.3
      } 
    }
  };

  const headerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.6,
        staggerChildren: 0.2
      } 
    }
  };

  const titleButtonVariants = {
    hidden: { opacity: 0, y: 10, scale: 0.95 },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: { duration: 0.5 } 
    },
    hover: {
      scale: 1.05,
      transition: { duration: 0.2 }
    }
  };
  
  const titleVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6 } 
    }
  };

  const cardContainerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };

  const buttonVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.5,
        type: "spring",
        stiffness: 200
      } 
    },
    hover: {
      scale: 1.05,
      transition: { 
        type: "spring", 
        stiffness: 400, 
        damping: 10 
      }
    }
  };

  const arrowVariants = {
    hover: {
      x: 5,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 10,
        repeat: Infinity,
        repeatType: "reverse"
      }
    }
  };

  return (
    <>
      <motion.section 
        className="section-box box-what-we-do"
        ref={sectionRef}
        initial="hidden"
        animate={isSectionInView ? "visible" : "hidden"}
        variants={sectionVariants}
      >
        <div className="container">
          <motion.div 
            className="text-center"
            ref={headerRef}
            variants={headerVariants}
            initial="hidden"
            animate={isHeaderInView ? "visible" : "hidden"}
          >
            <motion.div
              variants={titleButtonVariants}
              whileHover="hover"
            >
              <Link className="btn btn-brand-5" href="#">
                Our Users
              </Link>
            </motion.div>
            
            <motion.h2 
              className="heading-1 neutral-0 mt-15 mb-65"
              variants={titleVariants}
            >
              Who is OMX Sync For?
            </motion.h2>
          </motion.div>
          
          <motion.div 
            className="box-list-we-do"
            ref={cardsContainerRef}
            variants={cardContainerVariants}
            initial="hidden"
            animate={isCardsInView ? "visible" : "hidden"}
          >
            <div className="row">
              <div className="col-xl-3 col-lg-5">
                <FeatureCard 
                  image="/assets/imgs/page/omx-sales/Marketing & Sales Agencies.png"
                  title="Marketing & Sales Teams"
                  description="Manage campaigns & track task completion."
                  bottomImage="/assets/imgs/page/homepage4/img-web.png"
                  index={0}
                />
              </div>
              
              <div className="col-xl-4 col-lg-7">
                <FeatureCard 
                  image="/assets/imgs/page/omx-sync/Agencies & Consulting Firms.png"
                  title="Agencies & Consulting Firms"
                  description="Assign tasks & track client projects."
                  index={1}
                />
                
                <FeatureCard 
                  image="/assets/imgs/page/omx-sync/Manufacturing & Trading Businesses.png"
                  title="Manufacturing & Trading Businesses"
                  description="Automate workflows & manage teams."
                  cardClass="card-arrow-2"
                  index={2}
                />
              </div>
              
              <div className="col-xl-5 col-lg-12">
                <div className="row">
                  <div className="col-lg-12">
                    <FeatureCardWide 
                      image="/assets/imgs/page/omx-sync/Finance & Accounting Teams.png"
                      title="Finance & Accounting Teams"
                      description="Track approvals & expense management."
                      index={3}
                    />
                  </div>
                  <div className="col-lg-12">
                    <FeatureCardWide 
                      image="/assets/imgs/page/omx-sync/HR & Operations Teams.png"
                      title="HR & Operations Teams"
                      description="Automate attendance & leave tracking."
                      index={4}
                    />
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
          <div className="box-buttons justify-content-center mt-35">
            {/* <SyncCTA/> */}
            <Link className="btn btn-brand-4-medium hover-up" href="/form">
                            Book a Demo
                            <svg
                              width={22}
                              height={22}
                              viewBox="0 0 22 22"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M22 11.0003L18.4791 7.47949V10.3074H0V11.6933H18.4791V14.5213L22 11.0003Z"
                                fill="true"
                              ></path>
                            </svg>
              </Link>
          </div>
         
        </div>
      </motion.section>
    </>
  );
}

// Feature Card Component for standard cards
function FeatureCard({ image, title, description, bottomImage, cardClass = "", index }) {
  const cardRef = useRef(null);
  const isInView = useInView(cardRef, { once: true, amount: 0.2 });
  
  return (
    <motion.div 
      className={`card-features-6 ${cardClass} hover-up`}
      ref={cardRef}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ 
        duration: 0.7, 
        delay: index * 0.1,
        type: "spring",
        stiffness: 100
      }}
      whileHover={{ 
        y: -10,
        boxShadow: "0px 10px 20px rgba(0, 0, 0, 0.1)",
        transition: { type: "spring", stiffness: 300, damping: 15 }
      }}
    >
      <motion.div 
        className="card-image"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}
        transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
      >
        <img src={image} alt="OMX Sync" />
      </motion.div>
      
      <div className="card-info">
        <motion.h6
          initial={{ opacity: 0, y: 10 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 10 }}
          transition={{ duration: 0.4, delay: 0.3 + index * 0.1 }}
        >
          {title}
        </motion.h6>
        
        <motion.p 
          className="text-sm neutral-400"
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : { opacity: 0 }}
          transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
        >
          {description}
        </motion.p>
        
        {bottomImage && (
          <motion.img
            className="mt-25"
            src={bottomImage}
            alt="OMX Sync"
            initial={{ opacity: 0, y: 10 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 10 }}
            transition={{ duration: 0.4, delay: 0.5 + index * 0.1 }}
          />
        )}
      </div>
    </motion.div>
  );
}

// Feature Card Wide Component for the right column
function FeatureCardWide({ image, title, description, index }) {
  const cardRef = useRef(null);
  const isInView = useInView(cardRef, { once: true, amount: 0.2 });
  
  return (
    <motion.div 
      className="card-features-6 card-arrow-3 hover-up"
      ref={cardRef}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ 
        duration: 0.7, 
        delay: index * 0.1,
        type: "spring",
        stiffness: 100
      }}
      whileHover={{ 
        y: -10,
        boxShadow: "0px 10px 20px rgba(0, 0, 0, 0.1)",
        transition: { type: "spring", stiffness: 300, damping: 15 }
      }}
    >
      <motion.div 
        className="card-image"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}
        transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
      >
        <img src={image} alt="OMX Sync" />
      </motion.div>
      
      <div className="card-info">
        <div className="card-info-inner">
          <div className="card-info-left">
            <motion.h6
              initial={{ opacity: 0, y: 10 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 10 }}
              transition={{ duration: 0.4, delay: 0.3 + index * 0.1 }}
            >
              {title}
            </motion.h6>
            
            <motion.p 
              className="text-sm neutral-400"
              initial={{ opacity: 0 }}
              animate={isInView ? { opacity: 1 } : { opacity: 0 }}
              transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
            >
              {description}
            </motion.p>
          </div>
          <div className="card-info-right">
            {/* Button removed as per original code */}
          </div>
        </div>
      </div>
    </motion.div>
  );
}