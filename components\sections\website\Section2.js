"use client";
import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";
import VideoPopup from "@/components/elements/VideoPopup";
import Link from "next/link";

const AnimatedItem = ({ children, delay = 0 }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.3 });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 30 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
      transition={{ duration: 0.5, delay }}
    >
      {children}
    </motion.div>
  );
};

const AnimatedListItem = ({ children, index }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.3 });

  return (
    <motion.li
      ref={ref}
      initial={{ opacity: 0, x: -20 }}
      animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
      transition={{ duration: 0.3, delay: 0.1 * index }}
    >
      {children}
    </motion.li>
  );
};

export default function Section2() {
  return (
    <>
      <section className="section-box wow animate__animated animate__fadeIn box-case-study">
        <div className="container">
          <div className="text-center">
            <AnimatedItem>
              <h2 className="display-2 mb-70">
                What We Build
                <br className="d-none d-lg-block" /> (And Why It Works!)
              </h2>
            </AnimatedItem>
          </div>

          <div className="block-case-study">
            <div className="row align-items-center ">
              <div className="col-lg-7 ">
                <AnimatedItem>
                  <h2 className="heading-1 text-linear-3 mb-35">
                    High-Converting Landing Pages
                  </h2>
                </AnimatedItem>

                <AnimatedItem delay={0.2}>
                  <p className="text-lg neutral-0 mb-40">
                    Your ad spend is WASTED if your landing page isn't built to
                    convert traffic into leads & buyers.
                  </p>
                </AnimatedItem>

                <div className="box-list-check neutral-200 text-lg">
                  <ul className="list-check">
                    <AnimatedListItem index={0}>
                      <span className="">Funnel-Optimized Layouts </span>–
                      Designed to guide your visitor to ONE action.
                    </AnimatedListItem>

                    <AnimatedListItem index={1}>
                      Call-to-Action That Converts – No distractions, just pure
                      sales psychology.
                    </AnimatedListItem>

                    <AnimatedListItem index={2}>
                      Lightning-Fast Load Times – Because slow pages = lost
                      revenue.
                    </AnimatedListItem>

                    <AnimatedListItem index={3}>
                      Built-in CRM & Automations – Instantly capture & nurture
                      leads inside OMX Sales.
                    </AnimatedListItem>
                  </ul>
                </div>
              </div>

              <div className="col-lg-5 mb-30">
                <AnimatedItem delay={0.3}>
                <motion.img
                        src="/assets/imgs/page/website/web_3.png"
                        alt="Nivia"
                        initial={{ opacity: 0, scale: 0.9 }}
                        whileInView={{ opacity: 1, scale: 1 }}
                        viewport={{ once: true, amount: 0.5 }}
                        transition={{ duration: 0.6 }}
                      />
                  {/* <div className="box-case-gradient">
                    <div className="box-case-gradient-inner" />
                    <div className="box-case-gradient-bottom bg-white">
                      
                    </div>
                  </div> */}
                </AnimatedItem>
              </div>
            </div>
          </div>

          <br />

          <div className="block-case-study">
            <div className="row align-items-center">
              <div className="col-lg-5 mb-30">
                <AnimatedItem delay={0.2}>
                  <motion.img
                        src="/assets/imgs/page/website/web_4.png"
                        alt="Nivia"
                        initial={{ opacity: 0, scale: 0.9 }}
                        whileInView={{ opacity: 1, scale: 1 }}
                        viewport={{ once: true, amount: 0.5 }}
                        transition={{ duration: 0.6 }}
                      />
                  {/* <div className="box-case-gradient">
                    <div className="box-case-gradient-inner" />
                    <div className="box-case-gradient-bottom bg-white">
                      
                    </div>
                  </div> */}
                </AnimatedItem>
              </div>

              <div className="col-lg-7 ">
                <AnimatedItem>
                  <h2 className="heading-1 text-linear-3 mb-35">
                    Business Websites That Establish Authority & Trust
                  </h2>
                </AnimatedItem>

                <AnimatedItem delay={0.2}>
                  <p className="text-lg neutral-0 mb-40">
                    Your website is your online handshake—make sure it's STRONG!
                  </p>
                </AnimatedItem>

                <div className="box-list-check neutral-200 text-lg">
                  <ul className="list-check">
                    <AnimatedListItem index={0}>
                      Custom-Designed for Your Brand – No cookie-cutter
                      templates.
                    </AnimatedListItem>

                    <AnimatedListItem index={1}>
                      SEO-Optimized & Mobile-Responsive – So you get FREE
                      organic traffic.
                    </AnimatedListItem>

                    <AnimatedListItem index={2}>
                      {" "}
                      Lead Capture & Contact Forms – Turn visitors into leads on
                      autopilot.
                    </AnimatedListItem>

                    <AnimatedListItem index={3}>
                      Integrated with WhatsApp API & CRM – So leads don't just
                      visit… they CONVERT!
                    </AnimatedListItem>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <br />

          <div className="block-case-study">
            <div className="row align-items-center">
              <div className="col-lg-7 ">
                <AnimatedItem>
                  <h2 className="heading-1 text-linear-3 mb-35">
                    E-Commerce Stores That Sell (Even While You Sleep!)
                  </h2>
                </AnimatedItem>

                <AnimatedItem delay={0.2}>
                  <p className="text-lg neutral-0 mb-40">
                    Most e-commerce stores fail because they don't sell
                    properly. We fix that.
                  </p>
                </AnimatedItem>

                <div className="box-list-check neutral-200 text-lg">
                  <ul className="list-check">
                    <AnimatedListItem index={0}>
                      Conversion-Optimized Storefronts – Designed to make
                      visitors BUY.
                    </AnimatedListItem>

                    <AnimatedListItem index={1}>
                      WhatsApp & Chatbot Sales System – Engage customers & close
                      more deals via chat.
                    </AnimatedListItem>

                    <AnimatedListItem index={2}>
                      Built-in Cart Abandonment Recovery – So you stop losing
                      money on dropped carts.
                    </AnimatedListItem>

                    <AnimatedListItem index={3}>
                      Payment Gateway Integrations – Accept payments smoothly &
                      securely.
                    </AnimatedListItem>

                    <AnimatedListItem index={4}>
                      Inventory & Order Automation – Manage your store with ZERO
                      headaches.
                    </AnimatedListItem>
                  </ul>
                </div>
              </div>

              <div className="col-lg-5 mb-30">
                <AnimatedItem delay={0.3}>
                <motion.img
                        src="/assets/imgs/page/website/web_2.png"
                        alt="Nivia"
                        initial={{ opacity: 0, scale: 0.9 }}
                        whileInView={{ opacity: 1, scale: 1 }}
                        viewport={{ once: true, amount: 0.5 }}
                        transition={{ duration: 0.6 }}
                      />
                  {/* <div className="box-case-gradient">
                    <div className="box-case-gradient-inner" />
                    <div className="box-case-gradient-bottom bg-white">
                      
                    </div>
                  </div> */}
                </AnimatedItem>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
