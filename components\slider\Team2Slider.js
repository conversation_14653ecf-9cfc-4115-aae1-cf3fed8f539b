'use client'
import Link from 'next/link'
import Image from 'next/image'
import { Autoplay, Navigation, Pagination } from "swiper/modules"
import { Swiper, SwiperSlide } from "swiper/react"

const swiperOptions = {
    modules: [Autoplay, Pagination, Navigation],
    spaceBetween: 30,
    slidesPerView: 4,
    slidesPerGroup: 1,
    loop: true,
    navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev"
    },
    autoplay: {
        delay: 10000
    },
    breakpoints: {
        1199: {
            slidesPerView: 4
        },
        800: {
            slidesPerView: 3
        },
        500: {
            slidesPerView: 2
        },
        350: {
            slidesPerView: 1
        },
        250: {
            slidesPerView: 1
        }
    },
}

export default function Team2Slider() {
    return (
        <>
            <div className="swiper-container swiper-group-4">
                <Swiper {...swiperOptions}>
                    <SwiperSlide>
                        <div className="card-team-2">
                            <div className="card-image">
                                <Image
                                    src="/assets/imgs/page/about/k_j1-min.png"
                                    alt="Kamesh Jin<PERSON>"
                                    width={300}
                                    height={300}
                                    style={{ width: '100%', height: 'auto' }}
                                />
                            </div>
                            <div className="card-info">
                                <Link href="">
                                    <h6>Kamesh Jindal</h6>
                                </Link>
                                <p className="text-lg neutral-600">Founder</p> 
                                <p className="text-lg neutral-600">Operations, Finance</p>
                                <p className="text-lg neutral-600">DMIS Delhi Alum</p>
                            </div>
                        </div>
                    </SwiperSlide>
                    <SwiperSlide>
                        <div className="card-team-2">
                            <div className="card-image">
                                <Image
                                    src="/assets/imgs/page/about/Y_A-min.png"
                                    alt="Yash Agarwal"
                                    width={300}
                                    height={300}
                                    style={{ width: '100%', height: 'auto' }}
                                />
                            </div>
                            <div className="card-info">
                                <Link href="#">
                                    <h6>Yash Agarwal</h6>
                                </Link>
                                <p className="text-lg neutral-600">Founder</p> 
                                <p className="text-lg neutral-600">Technical, Implementation</p>
                                <p className="text-lg neutral-600">Christ University Bangalore</p>
                            </div>
                        </div>
                    </SwiperSlide>
                    <SwiperSlide>
                        <div className="card-team-2">
                            <div className="card-image">
                                <Image
                                    src="/assets/imgs/page/about/p_b1-min.png"
                                    alt="Prachi Bajla"
                                    width={300}
                                    height={300}
                                    style={{ width: '100%', height: 'auto' }}
                                />
                            </div>
                            <div className="card-info">
                                <Link href="#">
                                    <h6>Prachi Bajla</h6>
                                </Link>
                                <p className="text-lg neutral-600">Sales<br/> Storyteller</p>
                            </div>
                        </div>
                    </SwiperSlide>
                    <SwiperSlide>
                        <div className="card-team-2">
                            <div className="card-image">
                                <Image
                                    src="/assets/imgs/page/about/S_M-min.png"
                                    alt="Shivangi Mohpal"
                                    width={300}
                                    height={300}
                                    style={{ width: '100%', height: 'auto' }}
                                />
                            </div>
                            <div className="card-info">
                                <Link href="#">
                                    <h6>Shivangi Mohpal</h6>
                                </Link>
                                <p className="text-lg neutral-600">Marketing, Ai<br/>Symbiosis University, Pune</p>
                            </div>
                        </div>
                    </SwiperSlide>
                </Swiper>
            </div>
            <div className="box-button-slider box-button-slider-team">
                <div className="swiper-button-prev swiper-button-prev-testimonials swiper-button-prev-3">
                    <svg width={16} height={16} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6.66667 3.33398L2 8.00065M2 8.00065L6.66667 12.6673M2 8.00065H14" stroke="true" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round">
                        </path>
                    </svg>
                </div>
                <div className="swiper-button-next swiper-button-next-testimonials swiper-button-next-3">
                    <svg width={16} height={16} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9.33333 3.33398L14 8.00065M14 8.00065L9.33333 12.6673M14 8.00065H2" stroke="true" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round">
                        </path>
                    </svg>
                </div>
            </div>
        </>
    )
}
