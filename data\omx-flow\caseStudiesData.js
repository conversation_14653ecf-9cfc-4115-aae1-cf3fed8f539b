// Case Studies Main Data
const caseStudiesData = [
  {
    id: 1,
    heading: "Financial Institutions – AI-Powered Loan & Investment Assistance",
    client: "A leading Non-Banking Financial Company (NBFC) offering personal loans, home loans, and investment products across Tier 1 & Tier 2 cities",
    image: "/assets/imgs/page/omx-flow/caseimg/Case Study 1 Financial Institutions – AI-Powered Loan & Investment Assistance.png",
  },
  {
    id: 2,
    heading: "Educational Institutes – Automated Admission & Student Support",
    client: "A network of private schools with 8 campuses across two states",
    image: "/assets/imgs/page/omx-flow/caseimg/Case Study 2 Educational Institutes – Automated Admission & Student Support.png",
  },
  {
    id: 3,
    heading: "Digital Marketing Agencies – Lead Generation & Client Retention",
    client: "A performance marketing agency managing 40+ clients across industries",
    image: "/assets/imgs/page/omx-flow/caseimg/Case Study 3 Digital Marketing Agencies – Lead Generation & Client Retention.png",
  },
  {
    id: 4,
    heading: "Interior Designers – Streamlining Client Consultation & Project Updates",
    client: "A high-end interior design studio managing 8–10 projects monthly",
    image: "/assets/imgs/page/omx-flow/caseimg/Case Study 4 Interior Designers – Streamlining Client Consultation & Project Updates.png",
  },
  {
    id: 5,
    heading: "Corporate Offices – HR & Employee Engagement Automation",
    client: "A growing IT company with over 500 employees across 3 locations",
    image: "/assets/imgs/page/omx-flow/caseimg/Case Study 5 Corporate Offices – HR & Employee Engagement Automation.png",
  },
  {
    id: 6,
    heading: "Manufacturers – Dealer & B2B Customer Engagement",
    client: "A machinery manufacturing firm distributing through 200+ dealers",
    image: "/assets/imgs/page/omx-flow/caseimg/Case Study 6 Manufacturers – Dealer & B2B Customer Engagement.png",
  },
  {
    id: 7,
    heading: "Traders – Inventory & Customer Query Automation",
    client: "A B2B textile trader supplying to boutiques and resellers across India",
    image: "/assets/imgs/page/omx-flow/caseimg/Case Study 7 Traders – Inventory & Customer Query Automation.png",
  },
  {
    id: 8,
    heading: "Restaurants & Food Chains – Ordering & Customer Engagement",
    client: "A chain of fast-casual restaurants with 10 outlets",
    image: "/assets/imgs/page/omx-flow/caseimg/Case Study 8 Restaurants & Food Chains – Ordering & Customer Engagement.png",
  },
  {
    id: 9,
    heading: "Banking & Finance – AI-Powered Customer Support & Lead Qualification",
    client: "A private bank offering credit cards, loans, and investment services",
    image: "/assets/imgs/page/omx-flow/caseimg/Case Study 9 Banking & Finance – AI-Powered Customer Support & Lead Qualification.png",
  }
];

// Challenges Data
const challengesData = [
  {
    id: 1,
    subheading: "The NBFC was struggling with lead drop-offs and delayed responses:",
    p1: "Lead drop-offs due to delayed responses in loan inquiries",
    p2: "Sales reps were overwhelmed, especially during peak campaign seasons",
    p3: "Customers had to wait 24–48 hours to get answers about eligibility or product options",
  },
  {
    id: 2,
    subheading: "Their admissions team was overwhelmed during admission season:",
    p1: "Parents had to wait hours or even days for responses",
    p2: "Manual follow-ups were missed, and many leads went cold",
    p3: "No system to keep parents informed about student performance or fees",
  },
  {
    id: 3,
    subheading: "Agency teams struggled with lead management and client communication:",
    p1: "Difficult to manage lead follow-ups from ad campaigns",
    p2: "Leads were collected in spreadsheets and followed up manually",
    p3: "Clients demanded regular updates, creating additional communication overhead",
  },
  {
    id: 4,
    subheading: "The design studio faced challenges with client engagement and project visibility:",
    p1: "Initial inquiries via Instagram and website had poor response times",
    p2: "Clients were unaware of project progress, leading to frustration",
    p3: "Hard to maintain engagement with past clients for referrals or repeat business",
  },
  {
    id: 5,
    subheading: "HR was overwhelmed with repetitive tasks and employee management issues:",
    p1: "HR was inundated with repetitive questions about leave balance, policies, and holidays",
    p2: "Manual attendance and leave approval created bottlenecks",
    p3: "Employees felt disconnected from the organization",
  },
  {
    id: 6,
    subheading: "The manufacturing firm struggled with dealer communication and support:",
    p1: "Dealers complained about delays in support and updates",
    p2: "Couldn't keep up with order status inquiries or follow-ups for repeat purchases",
    p3: "Miscommunication was frequent between the firm and dealers",
  },
  {
    id: 7,
    subheading: "The textile trader faced challenges with customer inquiries and inventory management:",
    p1: "Customer inquiries were flooding their WhatsApp—mostly about pricing, availability, and delivery",
    p2: "Manual updates weren't scalable, leading to errors",
    p3: "Inefficient processes resulted in lost orders",
  },
  {
    id: 8,
    subheading: "The restaurant chain faced challenges with order management and customer retention:",
    p1: "Phone-based ordering led to missed calls and errors",
    p2: "No system to keep regular customers engaged",
    p3: "Reservations were chaotic, especially during weekends",
  },
  {
    id: 9,
    subheading: "The bank's support team was overwhelmed with routine inquiries and manual processes:",
    p1: "Support team overwhelmed by routine queries about account info, card issues, and documentation",
    p2: "Loan team spent time manually qualifying applicants, delaying the process",
    p3: "No efficient system for customer alerts and security updates",
  }
];

// Solutions Data
const solutionsData = [
  {
    id: 1,
    strong1: "Conversational AI Chatbot:",
    p1: "Deployed on WhatsApp to instantly handle loan eligibility checks and provide real-time responses",
    strong2: "Automated Workflows:",
    p2: "Set up to follow up with leads for document submissions, with personalized reminders on WhatsApp",
    strong3: "AI Assistant:",
    p3: "Handled FAQs about investment products, providing instant information 24/7",
  },
  {
    id: 2,
    strong1: "AI-Powered WhatsApp Bot:",
    p1: "Answered common admission queries instantly and allowed parents to track application status",
    strong2: "Payment Integration:",
    p2: "Integrated payment reminders and fee collection directly on WhatsApp",
    strong3: "Automated Reports:",
    p3: "Pushed weekly and monthly progress reports to parents based on teacher inputs",
  },
  {
    id: 3,
    strong1: "AI Chatbot Integration:",
    p1: "Integrated with ad landing pages to capture and qualify leads instantly on WhatsApp",
    strong2: "Automated Performance Updates:",
    p2: "Client performance dashboards with weekly updates sent via WhatsApp to clients",
    strong3: "Lead Nurturing Sequences:",
    p3: "Built in with automated follow-ups and upsell messages",
  },
  {
    id: 4,
    strong1: "AI Chatbot for Consultations:",
    p1: "Allowed users to request consultations, view portfolio, and get estimates instantly on WhatsApp",
    strong2: "Project Milestone Updates:",
    p2: "Clients received automatic updates on design approval, furniture dispatch, and installation schedule",
    strong3: "Client Retention System:",
    p3: "Past clients received automated messages with festive offers, new collections, and referral incentives",
  },
  {
    id: 5,
    strong1: "WhatsApp HR Chatbot:",
    p1: "Handled FAQs, leave requests, and policy queries 24/7",
    strong2: "Attendance Automation:",
    p2: "QR code-based attendance check-in integrated with geo-tracking for field teams",
    strong3: "Employee Engagement:",
    p3: "AI-driven announcements, surveys, and pulse checks kept teams engaged",
  },
  {
    id: 6,
    strong1: "Dealer WhatsApp Chatbot:",
    p1: "Provided instant access to product info, order tracking, and documentation",
    strong2: "Document & Order Automation:",
    p2: "Automated messages for invoice sharing, dispatch updates, and payment reminders",
    strong3: "Engagement Sequences:",
    p3: "AI sequences for re-engagement, new product launches, and special pricing offers",
  },
  {
    id: 7,
    strong1: "WhatsApp Query Automation:",
    p1: "Chatbot gave instant responses to inquiries, price lists, and offers",
    strong2: "Inventory Integration:",
    p2: "Integrated stock updates from the inventory system to reduce out-of-stock orders",
    strong3: "WhatsApp Ordering:",
    p3: "Implemented WhatsApp-based ordering with automated invoice generation",
  },
  {
    id: 8,
    strong1: "Digital Ordering:",
    p1: "WhatsApp chatbot for food ordering, table reservations, and menu access",
    strong2: "Loyalty System:",
    p2: "Automated messages reminding users of points or exclusive offers",
    strong3: "Feedback Collection:",
    p3: "Post-order feedback collection for quality control and retention",
  },
  {
    id: 9,
    strong1: "AI Support Chatbot:",
    p1: "Handled balance inquiries, application status, and common support issues on WhatsApp",
    strong2: "Automated Pre-qualification:",
    p2: "Workflows collected user info and scored leads automatically",
    strong3: "Security Alerts:",
    p3: "Real-time fraud alerts and security tips sent to customers through WhatsApp",
  }
];

// Results Data
const resultsData = [
  {
    id: 1,
    strong1: "40% increase in inquiry-to-application conversion",
    p1: "as customers received instant, helpful responses",
    strong2: "35% higher KYC completion rate",
    p2: "reducing time to disbursement",
    strong3: "50% lower customer support costs",
    p3: "as AI handled a significant portion of queries",
  },
  {
    id: 2,
    strong1: "5x faster response to admission inquiries",
    p1: "creating a better first impression",
    strong2: "50% reduction in counselor workload",
    p2: "allowing them to focus on high-priority leads",
    strong3: "Significant boost in parental trust and engagement",
    p3: "with regular updates delivered automatically",
  },
  {
    id: 3,
    strong1: "55% increase in lead conversion",
    p1: "due to faster engagement",
    strong2: "40% rise in client retention",
    p2: "thanks to improved communication and visibility",
    strong3: "30% fewer client queries",
    p3: "as they received proactive updates",
  },
  {
    id: 4,
    strong1: "70% faster first response time",
    p1: "led to more qualified consultations",
    strong2: "35% increase in repeat and referral business",
    p2: "through better engagement with past clients",
    strong3: "Higher client satisfaction",
    p3: "with transparent, real-time updates",
  },
  {
    id: 5,
    strong1: "50% reduction in HR support tickets",
    p1: "through automated responses to common queries",
    strong2: "60% improvement in attendance accuracy",
    p2: "with QR code and geo-tracking integration",
    strong3: "Noticeable boost in employee morale",
    p3: "due to timely updates and recognition",
  },
  {
    id: 6,
    strong1: "40% improvement in dealer satisfaction",
    p1: "through better communication and support",
    strong2: "50% reduction in order-related queries",
    p2: "with automated updates and tracking",
    strong3: "30% increase in repeat orders",
    p3: "via improved dealer relationships",
  },
  {
    id: 7,
    strong1: "65% reduction in manual query handling",
    p1: "through WhatsApp automation",
    strong2: "40% decrease in order cancellations",
    p2: "due to better stock visibility",
    strong3: "3x increase in order volume handled daily",
    p3: "with the same team size",
  },
  {
    id: 8,
    strong1: "30% increase in repeat customers",
    p1: "via loyalty engagement",
    strong2: "50% faster order processing",
    p2: "than phone-based system",
    strong3: "Higher customer satisfaction",
    p3: "from seamless digital experience",
  },
  {
    id: 9,
    strong1: "60% reduction in support call volumes",
    p1: "through AI-powered WhatsApp support",
    strong2: "50% faster loan application processing",
    p2: "with automated pre-qualification",
    strong3: "Improved customer trust",
    p3: "through proactive fraud alert communication",
  }
];

export { caseStudiesData, challengesData, solutionsData, resultsData };