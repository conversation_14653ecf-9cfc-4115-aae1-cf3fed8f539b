// Case Studies Main Data
const caseStudiesData = [
  {
    id: 1,
    heading: "Financial Services – Intelligent Lead Nurturing for High-Trust Sales",
    client: "A boutique wealth management firm offering financial planning and investment advice",
    image: "/assets/imgs/page/omx-sales/caseimg/Case Study 1 Financial Services – Intelligent Lead Nurturing for High-Trust Sales.png",
  },
  {
    id: 2,
    heading: "Education – Managing Inquiries at Scale During Admission Season",
    client: "A group of private K-12 schools across multiple cities",
    image: "/assets/imgs/page/omx-sales/caseimg/Case Study 2 Education – Managing Inquiries at Scale During AdmissionSeason.png",
  },
  {
    id: 3,
    heading: "Real Estate – Selling Luxury Properties with Speed & Precision",
    client: "A real estate developer of premium residential projects",
    image: "/assets/imgs/page/omx-sales/caseimg/Case Study 3 Real Estate – Selling Luxury Properties with Speed & Precision.png",
  },
  {
    id: 4,
    heading: "Digital Agencies – Managing Campaigns & Leads Without Chaos",
    client: "A performance marketing agency with 20+ active clients",
    image: "/assets/imgs/page/omx-sales/caseimg/Case Study 4 Digital Agencies – Managing Campaigns & Leads Without Chaos.png",
  },
  {
    id: 5,
    heading: "Healthcare – Keeping Patients Engaged Beyond Their Visit",
    client: "A multi-specialty hospital with departments like ortho, dental, and ENT",
    image: "/assets/imgs/page/omx-sales/caseimg/Case Study 5 Healthcare – Keeping Patients Engaged Beyond Their Visit.png",
  },
  {
    id: 6,
    heading: "Interior Design – Turning Interest into Signed Contracts",
    client: "A luxury interior design studio with high-ticket clientele",
    image: "/assets/imgs/page/omx-sales/caseimg/Case Study 6 Interior Design – Turning Interest into Signed Contracts.png",
  },
  {
    id: 7,
    heading: "Coaching & Webinars – Converting Webinar Views to Paid Clients",
    client: "A business coach running high-converting webinars and workshops",
    image: "/assets/imgs/page/omx-sales/caseimg/Case Study 7 Coaching & Webinars – Converting Webinar Views to Paid Clients.png",
  },
  {
    id: 8,
    heading: "SaaS Companies – Streamlining Inbound Demo Requests",
    client: "A SaaS firm offering HR tech solutions",
    image: "/assets/imgs/page/omx-sales/caseimg/Case Study 8 SaaS Companies – Streamlining Inbound Demo Requests.png",
  },
  {
    id: 9,
    heading: "Manufacturing – Selling B2B with Dealer Coordination",
    client: "A manufacturer of industrial-grade machinery",
    image: "/assets/imgs/page/omx-sales/caseimg/Case Study 9 Manufacturing – Selling B2B with Dealer Coordination.png",
  }
];

// Challenges Data
const challengesData = [
  {
    id: 1,
    subheading: "Financial services rely heavily on trust and timely communication. This firm was struggling with:",
    p1: "Leads falling through the cracks due to inconsistent follow-ups",
    p2: "Long sales cycles with high-value clients requiring personalized nurturing",
    p3: "No centralized view of client communication or sales status",
  },
  {
    id: 2,
    subheading: "During admissions season, the school received hundreds of inquiries weekly:",
    p1: "The admin team couldn't follow up with everyone",
    p2: "Many interested parents never received callbacks",
    p3: "They had no insight into which ad platforms were generating the best leads",
  },
  {
    id: 3,
    subheading: "Selling premium properties required exceptional responsiveness, but they faced issues with:",
    p1: "Their high-ticket clientele expected fast, professional responses",
    p2: "Many leads were unqualified, wasting the sales team's time",
    p3: "Site visits were often unorganized and poorly followed-up",
  },
  {
    id: 4,
    subheading: "Managing multiple client campaigns created organizational challenges:",
    p1: "Campaign leads came in from multiple platforms (Meta, Google, landing pages)",
    p2: "Their team was manually updating spreadsheets",
    p3: "Clients wanted real-time updates on campaign ROI",
  },
  {
    id: 5,
    subheading: "Patient care extended beyond the clinic visit, but they struggled with:",
    p1: "Patients missed follow-up appointments",
    p2: "Doctors had no easy way to recall patient history",
    p3: "Manual appointment reminders were ineffective",
  },
  {
    id: 6,
    subheading: "Converting high-value design projects required persistent follow-up:",
    p1: "Many leads requested design quotes but didn't convert",
    p2: "Follow-ups were delayed, and conversations got lost in WhatsApp or email",
    p3: "Designers wasted time chasing unqualified prospects",
  },
  {
    id: 7,
    subheading: "Webinar attendees represented significant potential revenue, but were lost due to:",
    p1: "70% of webinar attendees never got follow-up offers",
    p2: "Manual email outreach was time-consuming and inconsistent",
    p3: "Low course enrollment despite good webinar attendance",
  },
  {
    id: 8,
    subheading: "Demo requests and free trials weren't converting effectively:",
    p1: "Inbound demo requests were not being followed up quickly",
    p2: "Trial users often never returned to complete onboarding",
    p3: "Data was scattered across sales, support, and product teams",
  },
  {
    id: 9,
    subheading: "B2B sales through a distributor network created coordination challenges:",
    p1: "Leads came from trade shows, resellers, and ads but were poorly tracked",
    p2: "Dealer follow-ups were delayed, resulting in lost orders",
    p3: "No centralized visibility into pipeline or distributor performance",
  }
];

// Solutions Data
const solutionsData = [
  {
    id: 1,
    strong1: "AI-powered Lead Scoring:",
    p1: "Automatically prioritize leads based on engagement and potential value",
    strong2: "Multi-channel Nurturing:",
    p2: "Set up automated follow-ups via WhatsApp, email, and SMS to keep clients engaged",
    strong3: "CRM Integration:",
    p3: "Provided a 360° view of each client—past interactions, touchpoints, and deal stage",
  },
  {
    id: 2,
    strong1: "Automated Follow-Ups:",
    p1: "WhatsApp and email sequences triggered instantly upon form submission",
    strong2: "Centralized CRM:",
    p2: "All inquiries from websites, events, and ads were routed into a single, organized dashboard",
    strong3: "Attribution Tracking:",
    p3: "Tracked which marketing source (Google, Facebook, etc.) led to actual enrollments",
  },
  {
    id: 3,
    strong1: "Smart Lead Qualification:",
    p1: "Automatically segmented leads based on budget, location, and intent",
    strong2: "WhatsApp Automation:",
    p2: "Instant replies with brochures, floor plans, and personalized property info",
    strong3: "Site Visit Scheduler:",
    p3: "Synced with team calendars to automate visit booking and reminders",
  },
  {
    id: 4,
    strong1: "Ad Launcher Integration:",
    p1: "Connected with Google & Meta to monitor campaigns in one place",
    strong2: "Unified Lead Dashboard:",
    p2: "Captured all leads in a single CRM with status tracking",
    strong3: "Client Portal:",
    p3: "Delivered automated performance updates via email and dashboards",
  },
  {
    id: 5,
    strong1: "Automated Reminders:",
    p1: "WhatsApp & SMS reminders for consultations, follow-ups, and diagnostics",
    strong2: "CRM for Patient Records:",
    p2: "Doctors could access patient histories and communication logs",
    strong3: "Calendar Integration:",
    p3: "Doctors and admin staff synced to manage scheduling seamlessly",
  },
  {
    id: 6,
    strong1: "Automated Nurturing Sequences:",
    p1: "Follow-up messages with mood boards, portfolios, and proposals",
    strong2: "Proposal Tracker:",
    p2: "Tracked client interaction with proposals (opens, clicks, approvals)",
    strong3: "CRM with Interaction History:",
    p3: "Sales team could see all communications and client preferences",
  },
  {
    id: 7,
    strong1: "Automated Post-Webinar Sequences:",
    p1: "Drip campaigns via WhatsApp & email to nurture attendees",
    strong2: "CRM with Engagement Scoring:",
    p2: "Helped prioritize leads who clicked, viewed, or replied",
    strong3: "Sales Funnel Builder:",
    p3: "Built landing pages and checkout flows with upsells and follow-ups",
  },
  {
    id: 8,
    strong1: "Lead Routing & Automation:",
    p1: "Demo requests were instantly assigned and responded to",
    strong2: "Drip Campaigns for Trials:",
    p2: "Automated onboarding messages via email and WhatsApp",
    strong3: "Central CRM:",
    p3: "Unified customer info across sales, product, and support",
  },
  {
    id: 9,
    strong1: "Multi-Channel Lead Capture:",
    p1: "Automatically captured leads from web, WhatsApp, and events",
    strong2: "Distributor CRM Access:",
    p2: "Gave each dealer their dashboard to manage inquiries",
    strong3: "Automated Follow-Up Workflows:",
    p3: "Ensured consistent nurturing across all touchpoints",
  }
];

// Results Data
const resultsData = [
  {
    id: 1,
    strong1: "Lead conversion rate increased by 40%",
    p1: "due to consistent, smart follow-ups",
    strong2: "Advisor productivity improved",
    p2: "as sales time was focused on hot leads",
    strong3: "Customer satisfaction improved",
    p3: "as communication became faster and more relevant",
  },
  {
    id: 2,
    strong1: "Admissions increased by 50%",
    p1: "in the next admission cycle",
    strong2: "Lead response time dropped",
    p2: "from 2–3 days to under 2 hours",
    strong3: "Marketing efficiency improved",
    p3: "as budgets were reallocated to top-performing channels",
  },
  {
    id: 3,
    strong1: "Sales-qualified leads increased by 55%",
    p1: "through better lead qualification",
    strong2: "Sales team's conversion time decreased",
    p2: "due to better-qualified leads",
    strong3: "Site visit no-show rates dropped by 30%",
    p3: "thanks to automated confirmations",
  },
  {
    id: 4,
    strong1: "Lead response time improved by 65%",
    p1: "with centralized lead management",
    strong2: "Client satisfaction rose",
    p2: "as they saw real-time ROI updates",
    strong3: "Campaign optimization improved ad ROI by 50%",
    p3: "through better data analytics",
  },
  {
    id: 5,
    strong1: "Missed appointments dropped by 60%",
    p1: "with automated reminder sequences",
    strong2: "New patient inquiries rose",
    p2: "due to better follow-up flows",
    strong3: "Staff saved hours every week",
    p3: "thanks to automation",
  },
  {
    id: 6,
    strong1: "Lead-to-client conversion rate improved by 45%",
    p1: "through consistent follow-up",
    strong2: "Proposal approval turnaround time reduced by 35%",
    p2: "with tracking and reminders",
    strong3: "Sales cycle shortened by over 50%",
    p3: "helping book more projects",
  },
  {
    id: 7,
    strong1: "Webinar-to-sale conversion rate increased by 60%",
    p1: "with targeted follow-up sequences",
    strong2: "Manual follow-ups dropped by 80%",
    p2: "replaced by automation",
    strong3: "Funnel abandonment decreased significantly",
    p3: "thanks to engagement-based nurturing",
  },
  {
    id: 8,
    strong1: "Trial-to-paid conversions jumped by 55%",
    p1: "through better onboarding",
    strong2: "Onboarding drop-off reduced significantly",
    p2: "with timely engagement",
    strong3: "Sales & support teams collaborated better",
    p3: "with unified data",
  },
  {
    id: 9,
    strong1: "Dealer sales performance improved by 40%",
    p1: "with centralized lead management",
    strong2: "Lead response time dropped by over 60%",
    p2: "improving closure rates",
    strong3: "Conversion rate from trade show leads increased by 50%",
    p3: "due to systematic follow-up",
  }
];

export { caseStudiesData, challengesData, solutionsData, resultsData };