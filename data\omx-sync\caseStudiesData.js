// Case Studies Main Data
const caseStudiesData = [
  {
    id: 1,
    heading: "Financial Institutions – Task & Compliance Automation",
    client: "A mid-sized financial consulting firm in South India",
    image: "/assets/imgs/page/omx-sync/caseimg/Case Study 1 Financial Institutions – Task n Compliance Automation.jpg",
  },
  {
    id: 2,
    heading: "Schools & Educational Institutes – Academic & Staff Coordination",
    client: "A private university with over 5 departments and 100+ faculty members",
    image: "/assets/imgs/page/omx-sync/caseimg/Case Study 2 Schools & Educational Institutes – Academic & Staff Coordination.jpg",
  },
  {
    id: 3,
    heading: "Digital Marketing Agencies – Task & Client Management",
    client: "A performance marketing agency scaling to 20+ clients",
    image: "/assets/imgs/page/omx-sync/caseimg/Case Study 3 Digital Marketing Agencies – Task & Client Management.png",
  },
  {
    id: 4,
    heading: "Interior Designers – Project Execution & Vendor Coordination",
    client: "A boutique interior design studio managing high-end projects",
    image: "/assets/imgs/page/omx-sync/caseimg/case study4 Interior Designers.png",
  },
  {
    id: 5,
    heading: "Corporate Offices – Team Productivity & Remote Coordination",
    client: "A 100-member IT services company with hybrid teams",
    image: "/assets/imgs/page/omx-sync/caseimg/Case Study 5 Corporate Offices – Team Productivity & Remote Coordination.jpg",
  },
  {
    id: 6,
    heading: "Manufacturers – Production & Maintenance Automation",
    client: "A mid-scale manufacturing unit producing packaging materials",
    image: "/assets/imgs/page/omx-sync/caseimg/Case Study 6 Manufacturers – Production & Maintenance Automation.png",
  },
  {
    id: 7,
    heading: "Traders – Order Processing & Inventory Automation",
    client: "A B2B trading company handling bulk orders in hardware and tools",
    image: "/assets/imgs/page/omx-sync/caseimg/Case Study 7 Traders – Order Processing & Inventory Automation.png",
  }
];

// Challenges Data
const challengesData = [
  {
    id: 1,
    subheading: "Financial services operate under strict regulatory environments. This firm was facing:",
    p1: "Frequent compliance lapses due to missed deadlines, resulting in hefty penalties",
    p2: "Loss of client trust due to compliance issues",
    p3: "Internal tasks scattered across emails, spreadsheets, and paper notes causing confusion, delays, and miscommunication",
  },
  {
    id: 2,
    subheading: "Department heads and teachers were managing academic processes manually, leading to:",
    p1: "Timetable clashes and scheduling conflicts",
    p2: "Late submissions and slow administrative approvals",
    p3: "Valuable data lost in WhatsApp messages and email threads with no central system",
  },
  {
    id: 3,
    subheading: "Creative teams, ad strategists, and account managers faced significant coordination challenges:",
    p1: "Campaign timelines frequently slipped",
    p2: "Client approvals were constantly delayed",
    p3: "Tracking team activities became a full-time job in itself",
  },
  {
    id: 4,
    subheading: "Interior projects spanning months faced multiple coordination issues:",
    p1: "Multiple vendors from carpenters to light designers created communication challenges",
    p2: "Delays, miscommunication, and budget overruns were common",
    p3: "The founder was manually tracking timelines on Excel, often missing critical updates",
  },
  {
    id: 5,
    subheading: "After going hybrid post-COVID, the company struggled with remote work coordination:",
    p1: "Lack of visibility into employee productivity across locations",
    p2: "Task updates lost across Slack, emails, and spreadsheets",
    p3: "Expense approvals dragged for days, affecting project continuity",
  },
  {
    id: 6,
    subheading: "Manufacturing operations faced equipment and production management issues:",
    p1: "Machines often broke down due to poor maintenance scheduling",
    p2: "Production timelines were missed due to unclear task ownership",
    p3: "No way to measure worker productivity or assign performance goals",
  },
  {
    id: 7,
    subheading: "Order processing and inventory management were creating operational bottlenecks:",
    p1: "Order tracking was done manually, causing errors and delays",
    p2: "Expense tracking for supplier payments lacked structure",
    p3: "Inventory was always either overstocked or out of stock—hurting both cash flow and customer relationships",
  }
];

// Solutions Data
const solutionsData = [
  {
    id: 1,
    strong1: "Automated Compliance Reminders:",
    p1: "Using intelligent scheduling to set compliance deadlines with automatic reminders",
    strong2: "Task Management for Teams:",
    p2: "Each client file became a trackable task with designated roles, deadlines, and accountability",
    strong3: "Expense Tracking for Regulatory Reports:",
    p3: "Integrated expense logging enabled the finance team to generate audit-ready reports in minutes",
  },
  {
    id: 2,
    strong1: "Automated Lecture & Assignment Scheduling:",
    p1: "Professors could create recurring class schedules and assign deadlines using drag-and-drop tools",
    strong2: "Approval Workflows:",
    p2: "Admin processes like faculty leave, event planning, and vendor payments were streamlined with multi-level digital approvals",
    strong3: "Performance Dashboards:",
    p3: "Faculty could now view student performance trends department-wise and take proactive academic decisions",
  },
  {
    id: 3,
    strong1: "Automated Task Assignment:",
    p1: "Campaigns were broken into actionable tasks with deadlines and dependencies, assigned to the right team members",
    strong2: "Client Approval Workflows:",
    p2: "Clients received branded, trackable links for reviewing and approving creatives in a few clicks",
    strong3: "Live Project Dashboards:",
    p3: "Real-time visibility into task progress, timelines, and resource utilization",
  },
  {
    id: 4,
    strong1: "Phase-Wise Task Automation:",
    p1: "Projects were split into milestones (Design, Materials, Installation), each with automated tasks and reminders",
    strong2: "Vendor Coordination Tools:",
    p2: "Vendors received access to specific task boards, reducing back-and-forth and delays",
    strong3: "Expense Approval & Budget Controls:",
    p3: "Managers could approve or flag payments within the system, keeping budgets tight",
  },
  {
    id: 5,
    strong1: "Unified Task Dashboard:",
    p1: "Teams could log, assign, and track work in real time with mobile and desktop access",
    strong2: "Remote Coordination Tools:",
    p2: "Shared boards and live updates ensured alignment across departments and geographies",
    strong3: "Automated Expense Workflows:",
    p3: "Finance and HR teams could now approve reimbursements and budgets in hours, not days",
  },
  {
    id: 6,
    strong1: "Automated Maintenance Scheduling:",
    p1: "Equipment checks and servicing were scheduled, with alerts sent in advance",
    strong2: "Production Task Automation:",
    p2: "Daily targets were broken into line-level tasks with assigned teams and trackers",
    strong3: "Shift-Based Performance Dashboards:",
    p3: "Supervisors tracked who was on shift, how much was produced, and where delays occurred",
  },
  {
    id: 7,
    strong1: "Automated Order Task Assignments:",
    p1: "From order receipt to packaging and dispatch, every step was logged and tracked",
    strong2: "Inventory Alerts:",
    p2: "The system tracked stock levels in real time and sent alerts before critical items ran out",
    strong3: "Expense Workflow Automation:",
    p3: "Finance teams could tag and approve purchases directly linked to orders",
  }
];

// Results Data
const resultsData = [
  {
    id: 1,
    strong1: "90% improvement",
    p1: "in regulatory compliance adherence",
    strong2: "40% faster",
    p2: "loan processing and approvals",
    strong3: "60% boost",
    p3: "in timely client follow-ups",
  },
  {
    id: 2,
    strong1: "50% increase",
    p1: "in scheduling efficiency",
    strong2: "40% more",
    p2: "student submissions due to structured reminders",
    strong3: "3x faster",
    p3: "administrative approvals",
  },
  {
    id: 3,
    strong1: "35% faster",
    p1: "campaign execution",
    strong2: "50% quicker",
    p2: "client sign-off times",
    strong3: "45% improvement",
    p3: "in cross-team collaboration",
  },
  {
    id: 4,
    strong1: "40% reduction",
    p1: "in missed timelines",
    strong2: "2x improvement",
    p2: "in vendor collaboration",
    strong3: "30% decrease",
    p3: "in budget overruns",
  },
  {
    id: 5,
    strong1: "35% boost",
    p1: "in employee productivity",
    strong2: "60% smoother",
    p2: "remote team coordination",
    strong3: "3x faster",
    p3: "expense and leave approvals",
  },
  {
    id: 6,
    strong1: "50% reduction",
    p1: "in equipment downtime",
    strong2: "40% increase",
    p2: "in production efficiency",
    strong3: "30% improvement",
    p3: "in shift-level accountability",
  },
  {
    id: 7,
    strong1: "45% faster",
    p1: "order fulfillment",
    strong2: "50% reduction",
    p2: "in stock shortages",
    strong3: "70% improvement",
    p3: "in financial control",
  }
];

// Reflection Data (New Section)
const reflectionData = [
  {
    id: 1,
    strong1: "Financial institutions can't afford chaos.",
    p1: "OMX Sync turned this firm's operations from reactive firefighting to proactive, automated control. Now, compliance is met on time—every time."
  },
  {
    id: 2,
    strong1: "Education shouldn't be bogged down by paperwork.",
    p1: "OMX Sync helped this university focus on academic quality by removing the manual mess behind the scenes."
  },
  {
    id: 3,
    strong1: "Creative chaos turned into organized execution.",
    p1: "With OMX Sync, this agency scaled operations without burning out the team or compromising quality."
  },
  {
    id: 4,
    strong1: "Creativity thrives in structure.",
    p1: "OMX Sync gave this design firm a professional edge while retaining their artistic freedom."
  },
  {
    id: 5,
    strong1: "Remote doesn't have to mean disconnected.",
    p1: "OMX Sync brought back clarity, accountability, and momentum to hybrid work."
  },
  {
    id: 6,
    strong1: "A factory runs on precision.",
    p1: "OMX Sync made it possible to operate predictably, even under high demand."
  },
  {
    id: 7,
    strong1: "In trading, speed and accuracy make the sale.",
    p1: "OMX Sync helped this company eliminate chaos and scale operations with confidence."
  }
];

export { caseStudiesData, challengesData, solutionsData, resultsData, reflectionData };