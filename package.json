{"name": "nivia", "version": "0.1.0", "private": true, "authoe": "alithemes", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "sass": "sass --watch public/assets/scss/main.scss:public/assets/css/main.css", "postbuild": "next-sitemap"}, "dependencies": {"@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "framer-motion": "^12.23.10", "isotope-layout": "^3.0.6", "lucide-react": "^0.487.0", "next": "^14.2.30", "next-sitemap": "^4.2.3", "react": "18.2.0", "react-dom": "18.2.0", "react-fast-marquee": "^1.6.2", "react-intersection-observer": "^9.16.0", "react-modal-video": "^2.0.1", "sass": "^1.69.5", "swiper": "^11.0.4", "wowjs": "^1.1.3"}}