/**
 * CSS style for carouselTicker
 **/

.carouselTicker__list {
  margin: 10px 0;
  padding: 0;
  list-style-type: none;
  overflow: hidden;
}

.carouselTicker__item {
  margin: 0 0 0 0px;
  float: left;
  width: 100%;
  height: auto;
  text-align: center;
}

.carouselTicker__loader {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  /* background: #fff url("../images/loader.gif") center center no-repeat; */
}

/**
 * CSS style for vertical direction
 **/

.carouselTicker_vertical .carouselTicker__list {
  margin: 0;
}

.carouselTicker_vertical .carouselTicker__item {
  margin: 0 0 0px 0;
  width: 100%;
  height: auto;
  text-align: center;
}

#carouselTicker .carouselTicker__item,
#carouselTicker-destructor-example .carouselTicker__item,
#carouselTicker-buttons-controls-example .carouselTicker__item {
  width: auto;
  height: auto;
  line-height: normal;
}

.carouselTicker__item img {
  vertical-align: top;
}
